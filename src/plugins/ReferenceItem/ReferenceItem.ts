// // Draggable paragraphs
// import Paragraph from '@tiptap/extension-paragraph'

// export const CustomParagraph = Paragraph.extend({
//   name: "CustomParagraph",
//   addAttributes() {
//     // Return an object with attribute configuration
//     return {
//       color: {
//         default: 'pink',
//       },
//     }
//   },
//   addNodeView() {
//     return {
//       // Define how to render the node
//       // This is just an example, adjust as per your requirements
//       view(node, view, getPos, decorations) {
//         const dom = document.createElement('div');
//         dom.textContent = node.content; // Set the text content of the paragraph
//         dom.style.color = node.attrs.color; // Apply custom color attribute

//         // Add event listener for click, if needed
//         dom.addEventListener('click', () => {
//           // Handle click event
//         });

//         return {
//           dom
//         };
//       }
//     };
//   },
//   parseHTML() {
//     // Define how to parse HTML for this node type
//     return [
//       {
//         tag: 'div', // Assuming your custom paragraph is represented as a div
//         getAttrs: (element) => {
//           return {
//             // You may need to extract and parse attributes from the HTML element here
//             color: element.style.color || 'pink', // Example: Extract color attribute
//           };
//         },
//         getContent: (element) => {
//           return element.textContent; // Get the text content of the paragraph
//         },
//       },
//     ];
//   },
//   renderHTML() {
//     // Define how to render this node as HTML
//     return {
//       // This is just an example, adjust as per your requirements
//       tag: 'div',
//       content: (node) => node.content,
//       // You can also render other attributes here, such as color
//       // Example:
//       // attributes: {
//       //   style: `color: ${node.attrs.color || 'pink'};`
//       // }
//     };
//   },
// });


// Draggable paragraphs
import Paragraph from '@tiptap/extension-paragraph'

export const ReferenceItem = Paragraph.extend({
    name: "ReferenceItem",
    addCommands() {
      return {
        setImage: options => ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        }
      }
    },
    addAttributes() {
      // Return an object with attribute configuration
      return {
        // color: {
        //   default: 'pink',
        // },
        class:{
          default:'no-indent',
        }
      }
    },
  })
  