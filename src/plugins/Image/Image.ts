/* eslint-disable */
import { mergeAttributes, Node, nodeInputRule } from '@tiptap/core'
import type { Schema } from '@tiptap/pm/model'
import { Plugin } from '@tiptap/pm/state'
import { Decoration, DecorationSet, EditorView } from '@tiptap/pm/view'
import './upload-image.css'

export interface UploadFn {
  (file: File): Promise<string>
}

export interface SvgData {
  imageUrl: string
  [key: string]: any
}

export interface ImageOptions {
  /**
   * Controls if the image node should be inline or not.
   * @default false
   * @example true
   */
  inline: boolean

  /**
   * Controls if base64 images are allowed. Enable this if you want to allow
   * base64 image urls in the `src` attribute.
   * @default false
   * @example true
   */
  allowBase64: boolean

  /**
   * HTML attributes to add to the image element.
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, any>

  /**
   * 打开编辑器的回调函数
   */
  openSchematicEditor?: (data: SvgData) => void

  /**
   * 图片上传函数
   */
  uploadFn?: UploadFn
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    customImage: {
      /**
       * Add an image with SVG data support
       * @param options The image attributes
       */
      setImageWithSvgData: (options: {
        src: string
        alt?: string
        title?: string
        svgData?: any
      }) => ReturnType
      /**
       * Update image SVG data
       * @param src The image source URL
       * @param svgData The SVG data to store
       */
      updateImageSvgData: (src: string, svgData: any) => ReturnType
    }
  }
}

/**
 * Matches an image to a ![image](src "title") on input.
 */
export const inputRegex = /(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/

/**
 * This extension allows you to insert images.
 * @see https://www.tiptap.dev/api/nodes/image
 */
export const Image = Node.create<ImageOptions>({
  name: 'image',
  onCreate() {
    if (typeof this.options.uploadFn !== 'function') {
      console.warn('uploadFn should be a function')
      return
    }
    uploadFn = this.options.uploadFn
  },
  onDestroy() {
    // 清理所有编辑按钮和相关资源
    const editButtons = document.querySelectorAll('.image-edit-button')
    editButtons.forEach((button) => {
      const observer = (button as any)._observer
      const imageObserver = (button as any)._imageObserver
      const checkInterval = (button as any)._checkInterval

      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect()
      }

      if (imageObserver && typeof imageObserver.disconnect === 'function') {
        imageObserver.disconnect()
      }

      if (checkInterval && typeof clearInterval === 'function') {
        clearInterval(checkInterval)
      }

      button.remove()
    })

    // 清理所有图片上的点击超时
    const images = document.querySelectorAll('img')
    images.forEach((img) => {
      const clickTimeout = (img as any)._clickTimeout
      if (clickTimeout && typeof clearTimeout === 'function') {
        clearTimeout(clickTimeout)
      }
    })
  },
  addOptions() {
    return {
      inline: false,
      allowBase64: false,
      HTMLAttributes: {},
      uploadFn: async () => {
        return ''
      },
      openSchematicEditor: () => {}
    }
  },

  inline() {
    return this.options.inline
  },

  group() {
    return this.options.inline ? 'inline' : 'block'
  },

  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null
      },
      alt: {
        default: null
      },
      title: {
        default: null
      },
      svgData: {
        default: null,
        parseHTML: (element) => {
          const svgData = element.getAttribute('data-svg-data')
          return svgData ? JSON.parse(svgData) : null
        },
        renderHTML: (attributes) => {
          if (!attributes.svgData) return {}
          return {
            'data-svg-data': JSON.stringify(attributes.svgData)
          }
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: this.options.allowBase64 ? 'img[src]' : 'img[src]:not([src^="data:"])',
        getAttrs: (element) => {
          const el = element as HTMLElement
          const svgDataAttr = el.getAttribute('data-svg-data')
          let svgData = null

          if (svgDataAttr) {
            try {
              svgData = JSON.parse(svgDataAttr)
            } catch (error) {
              console.warn('解析data-svg-data属性失败:', error)
            }
          }

          return {
            src: el.getAttribute('src'),
            alt: el.getAttribute('alt'),
            title: el.getAttribute('title'),
            width: el.getAttribute('width'),
            height: el.getAttribute('height'),
            svgData
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // 处理 svgData 属性，将其转换为 data-svg-data 属性
    const attrs = { ...HTMLAttributes }
    if (attrs.svgData) {
      attrs['data-svg-data'] = JSON.stringify(attrs.svgData)
      delete attrs.svgData // 移除 svgData，因为它不是标准的 HTML 属性
    }
    return ['img', mergeAttributes(this.options.HTMLAttributes, attrs)]
  },

  addCommands() {
    return {
      setImage:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options
          })
        },
      setImageWithSvgData:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options
          })
        },
      updateImageSvgData:
        (src: string, svgData: any) =>
        ({ tr, state }) => {
          const { doc } = state
          let updated = false

          doc.descendants((node, pos) => {
            if (node.type.name === 'image' && node.attrs.src === src) {
              tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                svgData
              })
              updated = true
              return false // 停止遍历
            }
          })

          return updated
        },
      addImage: () => () => {
        const fileHolder = document.createElement('input')
        fileHolder.setAttribute('type', 'file')
        fileHolder.setAttribute('accept', 'image/*')
        fileHolder.setAttribute('style', 'visibility:hidden')
        document.body.appendChild(fileHolder)

        const view = this.editor.view
        const schema = this.editor.schema

        fileHolder.addEventListener('change', (e: Event) => {
          if (
            view.state.selection.$from.parent.inlineContent &&
            (<HTMLInputElement>e.target)?.files?.length
          ) {
            startImageUpload(view, (<HTMLInputElement>e.target)?.files![0], schema)
          }
          view.focus()
        })
        fileHolder.click()
        return true
      }
    }
  },
  addProseMirrorPlugins() {
    const options = this.options
    return [
      placeholderPlugin,
      new Plugin({
        props: {
          handleDOMEvents: {
            dblclick(_view, event) {
              console.log('dblclick')
              event.stopPropagation()
              return false
            },
            click(view, event) {
              const target = event.target as HTMLElement

              // 如果点击的是编辑按钮或其子元素，不做处理
              if (target.closest('.image-edit-button')) {
                event.stopPropagation()
                return true
              }

              // 如果点击的是图片，检查是否有SVG数据，然后添加编辑按钮
              if (target.tagName === 'IMG') {
                const src = target.getAttribute('src')
                if (src) {
                  // 使用防抖机制，避免快速多次点击
                  if ((target as any)._clickTimeout) {
                    clearTimeout((target as any)._clickTimeout)
                  }

                  ;(target as any)._clickTimeout = setTimeout(() => {
                    // 延迟检查选中状态，确保 ProseMirror 完成状态更新
                    const selectedNode = target.closest('.ProseMirror-selectednode')
                    if (selectedNode) {
                      // 检查是否有SVG数据
                      let hasSvgData = false
                      view.state.doc.descendants((node: any) => {
                        if (
                          node.type.name === 'image' &&
                          node.attrs.src === src &&
                          node.attrs.svgData
                        ) {
                          hasSvgData = true
                          return false // 停止遍历
                        }
                      })

                      // 只有有SVG数据的图片才添加编辑按钮
                      if (hasSvgData) {
                        addEditButtonToSvg(target, src, options, view)
                      }
                    }
                  }, 100) // 100ms防抖延迟
                }
              }

              return true
            }
          }
        }
      })
    ]
  },
  addInputRules() {
    return [
      nodeInputRule({
        find: inputRegex,
        type: this.type,
        getAttributes: (match) => {
          const [, , alt, src, title] = match

          return { src, alt, title }
        }
      })
    ]
  }
})

let uploadFn: UploadFn
let imagePreview = ''
//Plugin for placeholder
const placeholderPlugin = new Plugin({
  state: {
    init() {
      return DecorationSet.empty
    },
    apply(tr, set) {
      // Adjust decoration positions to changes made by the transaction
      set = set.map(tr.mapping, tr.doc)
      // See if the transaction adds or removes any placeholders
      //@ts-ignore :
      const action = tr.getMeta(this)
      if (action && action.add) {
        const widget = document.createElement('div')
        const img = document.createElement('img')
        widget.classList.value = 'image-uploading'
        img.src = imagePreview
        widget.appendChild(img)
        const deco = Decoration.widget(action.add.pos, widget, {
          id: action.add.id
        })
        set = set.add(tr.doc, [deco])
      } else if (action && action.remove) {
        set = set.remove(set.find(undefined, undefined, (spec) => spec.id == action.remove.id))
      }
      return set
    }
  },
  props: {
    decorations(state) {
      return this.getState(state)
    }
  }
})

//Find the placeholder in editor
function findPlaceholder(state: any, id: any) {
  const decos = placeholderPlugin.getState(state)
  const found = decos?.find(undefined, undefined, (spec) => spec.id == id)

  return found?.length ? found[0].from : null
}

function startImageUpload(view: EditorView, file: File, schema: Schema) {
  imagePreview = URL.createObjectURL(file)
  // A fresh object to act as the ID for this upload
  const id = {}

  // Replace the selection with a placeholder
  const tr = view.state.tr
  if (!tr.selection.empty) tr.deleteSelection()
  tr.setMeta(placeholderPlugin, { add: { id, pos: tr.selection.from } })
  view.dispatch(tr)
  uploadFn(file).then(
    (url) => {
      const pos = findPlaceholder(view.state, id)
      // If the content around the placeholder has been deleted, drop
      // the image
      if (pos == null) return
      // Otherwise, insert it at the placeholder's position, and remove
      // the placeholder
      view.dispatch(
        view.state.tr
          .replaceWith(pos, pos, schema.nodes.image.create({ src: url }))
          .setMeta(placeholderPlugin, { remove: { id } })
      )
    },
    (_error) => {
      // On failure, just clean up the placeholder
      view.dispatch(tr.setMeta(placeholderPlugin, { remove: { id } }))
    }
  )
}

// 添加编辑按钮到SVG图片
async function addEditButtonToSvg(
  img: HTMLElement,
  src: string,
  options: ImageOptions,
  editorView?: any
) {
  if (!src) return

  let svgData: any = null

  // 方法1: 尝试从编辑器状态中直接获取SVG数据
  if (editorView) {
    try {
      editorView.state.doc.descendants((node: any) => {
        if (node.type.name === 'image' && node.attrs.src === src && node.attrs.svgData) {
          svgData = node.attrs.svgData

          return false // 停止遍历
        }
      })
    } catch (error) {
      console.warn('从编辑器状态获取SVG数据失败:', error)
    }
  }

  // 方法2: 尝试从图片的data-svg-data属性中获取SVG数据
  if (!svgData) {
    const svgDataAttr = img.getAttribute('data-svg-data')
    if (svgDataAttr) {
      try {
        svgData = JSON.parse(svgDataAttr)
      } catch (error) {}
    }
  }

  // 方法3: 尝试从父元素获取data-svg-data属性
  if (!svgData && img.parentElement) {
    const parentSvgDataAttr = img.parentElement.getAttribute('data-svg-data')

    if (parentSvgDataAttr) {
      try {
        svgData = JSON.parse(parentSvgDataAttr)
      } catch (error) {
        console.warn('解析父元素SVG数据失败:', error)
      }
    }
  }

  // 方法4: 尝试从所有可能的祖先元素中查找
  if (!svgData) {
    let currentElement = img.parentElement
    while (currentElement && !svgData) {
      const ancestorSvgData = currentElement.getAttribute('data-svg-data')
      if (ancestorSvgData) {
        try {
          svgData = JSON.parse(ancestorSvgData)

          break
        } catch (error) {
          console.warn('解析祖先元素SVG数据失败:', error)
        }
      }
      currentElement = currentElement.parentElement
    }
  }

  if (!svgData) {
    return
  }

  const parent = img.parentElement
  if (!parent) return

  // 检查是否已存在编辑按钮，如果存在则先移除
  const existingButton = parent.querySelector('.image-edit-button')
  if (existingButton) {
    // 清理已存在的按钮及其相关资源
    const existingObserver = (existingButton as any)._observer
    if (existingObserver && typeof existingObserver.disconnect === 'function') {
      existingObserver.disconnect()
    }
    existingButton.remove()
  }

  // 创建编辑按钮
  const editButton = document.createElement('div')
  editButton.className = 'image-edit-button active'
  editButton.innerHTML = `
    <svg viewBox="0 0 24 24" width="14" height="14">
      <path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"></path>
    </svg>
    <span>编辑</span>
  `

  // 添加到DOM
  parent.appendChild(editButton)

  // 添加点击事件
  const handleEditClick = (e: Event) => {
    e.preventDefault()
    e.stopPropagation()

    if (options.openSchematicEditor) {
      options.openSchematicEditor(svgData)
    } else {
      console.warn('openSchematicEditor 回调函数未定义')
    }
  }

  editButton.addEventListener('click', handleEditClick)

  // 创建MutationObserver来监听选中状态变化
  const observer = new MutationObserver(() => {
    const isSelected = img.closest('.ProseMirror-selectednode')
    if (!isSelected && editButton.parentNode) {
      // 清理资源
      editButton.removeEventListener('click', handleEditClick)
      observer.disconnect()
      editButton.remove()
    }
  })

  // 将observer引用存储到按钮上，以便后续清理
  ;(editButton as any)._observer = observer

  // 观察父级元素的 class 变化
  const selectedNode = img.closest('.ProseMirror-selectednode')
  if (selectedNode) {
    observer.observe(selectedNode, {
      attributes: true,
      attributeFilter: ['class']
    })
  } else {
    // 如果没有找到选中节点，立即清理
    editButton.removeEventListener('click', handleEditClick)
    observer.disconnect()
    editButton.remove()
  }

  // 添加额外的安全检查：如果图片被移除，也要清理编辑按钮
  const imageObserver = new MutationObserver(() => {
    if (!img.parentNode || !editButton.parentNode) {
      editButton.removeEventListener('click', handleEditClick)
      observer.disconnect()
      imageObserver.disconnect()
      if (editButton.parentNode) {
        editButton.remove()
      }
    }
  })

  imageObserver.observe(document.body, {
    childList: true,
    subtree: true
  })

  // 将imageObserver也存储到按钮上
  ;(editButton as any)._imageObserver = imageObserver

  // 添加定期检查，确保图片仍然可见且有效
  const checkInterval = setInterval(() => {
    if (!img.parentNode || !editButton.parentNode || !img.offsetParent) {
      clearInterval(checkInterval)
      editButton.removeEventListener('click', handleEditClick)
      observer.disconnect()
      imageObserver.disconnect()
      if (editButton.parentNode) {
        editButton.remove()
      }
    }
  }, 1000) // 每秒检查一次

  // 将checkInterval也存储到按钮上
  ;(editButton as any)._checkInterval = checkInterval
}
