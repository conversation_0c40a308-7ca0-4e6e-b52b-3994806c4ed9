/* eslint-disable */
// @ts-nocheck

import type { AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios'
import axios from 'axios'
import type { ResponseBody } from '@/api/typing'
import { storage } from '@/utils/local-storage'
import { HTTP_STATUS, isPcWeb } from '@/utils/constants'
import { clearObjectNullParam, isXiaoin } from '@/utils/utils'
import { TOKENNAME } from '@/config/config'
import { requestSignHandler } from './request-interceptor-sign'
import { getBspId, isLocalHost } from './cross'
// import qs from 'qs'
import emitter from '@/utils/mitt'
import { ElMessage } from 'element-plus'
import { message } from 'ant-design-vue'

// 这里是用于设定请求后端时，所用的 Token KEY
// 可以根据自己的需要修改，常见的如 Access-Token，Authorization
// 需要注意的是，请尽量保证使用中横线`-` 来作为分隔符，
// 避免被 nginx 等负载均衡器丢弃了自定义的请求头
export const REQUEST_TOKEN_KEY = TOKENNAME

const toLogin = () => {
  console.log('toLogin')
  if (isLocalHost()) {
    emitter.emit('visibleLoginModal', true)
  } else {
    ElMessage.error('请先登录')
    const href = encodeURIComponent(window.location.href)
    window.location.href = `/home/<USER>
  }
}

const request = axios.create({
  // API 请求的默认前缀
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  timeout: 600000 // 请求超时时间
})

export type RequestError = AxiosError<{
  message?: string
  result?: any
  errorMessage?: string
}>

// 异常拦截处理器
const errorHandler = (error: RequestError): Promise<any> => {
  console.log('errorHandler', errorHandler)
  console.log('error ==>', error)
  const newError: any = { ...error }
  if (error.response) {
    const { data = {}, status, statusText } = error.response
    // 403 无权限
    if (status === HTTP_STATUS.FORBIDDEN) {
      //Notify.danger((data && data.message) || statusText)
    }
    // 401 未登录/未授权
    if (status === HTTP_STATUS.AUTHENTICATE) {
      //Notify.danger('Authorization verification failed')
      toLogin()
      // deleteAuthentication()
      //TODO
      // 如果你需要直接跳转登录页面
      // router.replace({ name: 'square' })
      // RouteService.pageToHome()
      // Taro.showLoading({ title: '重新登录中' })
      // setTimeout(() => {
      //   Taro.hideLoading()
      //   RouteService.pageToHome()
      // }, 1000)
    }
    if (status === HTTP_STATUS.SERVER_ERROR) {
      // newError['message'] =
    }
  } else {
    // 网络错误
    if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
      ElMessage.error('网络错误，请检查您的网络连接') // 网络错误提示
    } else {
      // 如果是其他错误
      newError['statusText'] = '网络错误'
      ElMessage.error('未知错误，请稍后再试') // 默认错误提示
    }
  }
  return Promise.reject(newError)
}

const deleteAuthentication = () => {
  storage.remove(TOKENNAME)
  storage.remove('userInfo')
  storage.remove('openid')
  storage.remove('userId')
  storage.remove('loginStatus')
}
// 请求拦截器
const requestHandler = (
  config: AxiosRequestConfig
): AxiosRequestConfig | Promise<AxiosRequestConfig> => {
  const savedToken = storage.get(TOKENNAME)
  // 如果 token 存在
  // 让每个请求携带自定义 token, 请根据实际情况修改
  if (config.headers) {
    config.headers['bspId'] = getBspId()
    if (!isXiaoin()) {
      config.headers['shell-name'] = 'ybxz'
    }
    if (savedToken) {
      config.headers['Authorization'] = `Bearer ${savedToken}`
      config.headers['token'] = `${savedToken}`
    }
  }
  if (config.method === 'post') {
    if (config.params && config.params.platform) {
      if (config.data && typeof config.data === 'object') {
        config.params = clearObjectNullParam({
          ...(config.params ? config.params : {})
        })
      }
    } else {
      if (config.data && typeof config.data === 'object') {
        config.params = clearObjectNullParam({
          ...(config.params ? config.params : {}),
          platform: isPcWeb() ? 'web' : 'wap'
        })
      }
    }
  }
  if (config.method === 'get') {
    if (config.params && config.params.platform) {
      config.params = clearObjectNullParam({
        ...(config.params ? config.params : {})
      })
    } else {
      config.params = clearObjectNullParam({
        ...(config.params ? config.params : {}),
        platform: isPcWeb() ? 'web' : 'wap'
      })
    }
  }
  // console.log(config.params, config.url, 'config.params')
  // config.url =
  //   config.url + '?' + qs.stringify(config.params, { allowDots: true, arrayFormat: 'repeat' })
  // console.log(config.params, config.url, 'config.params1')

  return config
}

// Add a request interceptor
request.interceptors.request.use(requestSignHandler, errorHandler)
request.interceptors.request.use(requestHandler, errorHandler)
// 响应拦截器
const responseHandler = (
  response: AxiosResponse
): ResponseBody<any> | AxiosResponse<any> | Promise<any> | any => {
  // console.log("ResponseBody ==>", response)
  if (response.status == HTTP_STATUS.AUTHENTICATE) {
    deleteAuthentication()
    return {
      ok: false,
      code: '401',
      message: ''
    }
  }

  let success = true
  let code = 0
  if (response.status == 200) {
    const res = response.data as ResponseBody
    // if (!res) {
    //   return {
    //     ok: false,
    //     success,
    //     code: 500,
    //     message: '未知错误'
    //   }
    // }
    // 把外面的success也加入到data中
    response.data = res.result
    success = res.success
    code = res.code

    const message = res.message || '未知错误'
    if (res.code !== HTTP_STATUS.SUCCESS) {
      if (!success) {
        ElMessage.error(res.message || '未知错误')
      }
      if (res.code === HTTP_STATUS.AUTHENTICATE) {
        // deleteAuthentication()
        toLogin()
      }
      return {
        ok: false,
        success,
        code,
        message: message
      }
    }
    // 成功是，如果判断带有更新的token， 则写入
    if (res.newToken) {
      storage.set(TOKENNAME, res.newToken)
    }
  } else if (response.status == HTTP_STATUS.SERVER_ERROR) {
    const res = response.data as ResponseBody
    success = res.success
    code = res.code
    if (!success) {
      ElMessage.error(res.message || '未知错误')
    }
    return {
      ...response,
      success,
      code,
      message: res.message || '未知错误',
      ok: false
    }
  }
  return {
    ...response,
    ok: true,
    success,
    code
  }
}

// Add a response interceptor
request.interceptors.response.use(responseHandler, errorHandler)

export { AxiosResponse }

export default request
