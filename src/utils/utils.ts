/* eslint-disable */
import { Editor } from '@tiptap/vue-3'

import { Mark } from '@tiptap/core'

import StarterKit from '@tiptap/starter-kit'
import { Underline } from '@tiptap/extension-underline'
// import Table from '@tiptap/extension-table'
// import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import Superscript from '@tiptap/extension-superscript'
import Paragraph from '@tiptap/extension-paragraph'

// 导入自定义标注扩展
import AnnotationSuperscript from '@/extensions/AnnotationSuperscript'

//import tiptap pro extensions
import Mathematics from '@tiptap-pro/extension-mathematics'
import TableOfContents from '@tiptap-pro/extension-table-of-contents'

//import third party extensions
import ImageResize from 'tiptap-extension-resize-image'

import Table from '@/extensions/table'
// import Bold from '@/extensions/bold'
import type { CosSignResultInfo, CosUploadParams } from '@/services/types/appMessage'
import COS from 'cos-js-sdk-v5'
import { ReferenceItem } from '@/plugins/ReferenceItem/ReferenceItem'
import UploadImage from '@/plugins/Image'
// import ImageResize from "@/plugins/ImageResize";

import { getFileCosSign, ossGetSign, uploadByUrl } from '@/api/upload'
import { ElMessage } from 'element-plus'
import { rewritingExpense } from '@/utils/constants'
import { MathBlock, MathInline } from '@/components/extensions/MathExtension'
import { Chart } from '@/components/extensions/ChartExtension'
import { CustomImage } from '@/components/extensions/ImageExtension'
// @ts-ignore
import OSS from 'ali-oss'

import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from 'chart.js'
import Dropcursor from '@tiptap/extension-dropcursor'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import TableRow from '@tiptap/extension-table-row'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

//Optional
// 定义一个高亮标记
const Highlight = Mark.create({
  name: 'highlight',
  addAttributes() {
    return {
      class: 'highlight'
    }
  },
  parseHTML() {
    return [
      {
        tag: 'span[class="highlight"]'
      }
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', HTMLAttributes, 0]
  }
})

const CustomMathematics = Mathematics.extend({
  name: 'CustomMathematics',
  addNodeView() {
    return () => {
      const container = document.createElement('div')

      container.addEventListener('click', (event) => {
        alert('clicked on the container')
      })

      const content = document.createElement('div')
      container.append(content)
      console.log('CustomMathematics addNodeView')
      return {
        dom: container,
        contentDOM: content
      }
    }
  }
})

interface createTipTapEditorParams {
  defaultContent?: any
  onFocus?: (params: { editor: any }) => void
  onUpdate?: (params: { editor: any }) => void
  handleChartEdit?: (params: { type: string; data: any }) => void
  handleMathEdit?: (params: { formula: string; type: string }) => void
  openSchematicEditor?: (svgData: { imageUrl: string; [key: string]: any }) => void
}

export const createTipTapEditor = (params: createTipTapEditorParams & { readonly?: boolean }) => {
  const {
    defaultContent,
    onFocus,
    onUpdate,
    readonly,
    handleChartEdit,
    handleMathEdit,
    openSchematicEditor
  } = params

  return new Editor({
    extensions: [
      StarterKit.configure({
        paragraph: {
          HTMLAttributes: {
            class: 'my-custom-paragraph'
          }
        },
        dropcursor: false
      }),
      // Paragraph,
      Underline,
      Highlight,
      Table.configure({
        resizable: true
      }),
      TableRow,
      TableHeader,
      TableCell,

      Dropcursor,
      ImageResize,
      UploadImage.configure({
        uploadFn: uploadFnOSS,
        openSchematicEditor
      }),
      Mathematics,
      TableOfContents.configure({
        anchorTypes: ['heading', 'customAnchorType']
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph']
      }),
      Superscript,
      AnnotationSuperscript, // 添加自定义标注扩展
      ReferenceItem,
      Chart.configure({
        onEditChart: handleChartEdit
      }),
      MathInline.configure({
        onEdit: handleMathEdit
      }),
      MathBlock.configure({
        onEdit: handleMathEdit
      })
    ],
    content: defaultContent,
    onSelectionUpdate: () => {
      // isAIChangeChecked.value = false;
      // isAIChanging.value = false;
      // isAIThinking.value = false;
      // activeAIText.value = "";
    },
    onFocus: onFocus,
    onTransaction() {
      // console.log('editor1 onTransaction')
    },
    onUpdate,
    editable: !readonly // 确保编辑器可编辑
  })
}

export const isEmpty = (obj: any) => {
  if (typeof obj === 'undefined' || obj === null || obj === '') {
    return true
  } else {
    return false
  }
}
export const clearObjectNullParam = (obj: any) => {
  /* 删除空值 */
  Object.keys(obj).forEach((item) => {
    if (isEmpty(obj[item])) {
      delete obj[item]
    }
  })
  return obj
}

export const isInTestServer = (): boolean => {
  const url = import.meta.env.VITE_APP_API_BASE_URL || ''
  return url.indexOf('lbm-helper-test') > -1
}

export const countWords = (str: string) => {
  if (!str) {
    return 0
  }
  const chinese = Array.from(str).filter((ch) => /[\u4e00-\u9fa5]/.test(ch)).length

  const english = Array.from(str)
    .map((ch) => (/[a-zA-Z0-9\s]/.test(ch) ? ch : ' '))
    .join('')
    .split(/\s+/)
    .filter((s) => s).length

  return chinese + english
}
export const isXioin = window.location.hostname.indexOf('xiaoin.cn') > -1

// export const uploadFn = async (file: any) => {
//   try {
//     const item = file
//     const result = await getFileCosSign({ filename: item.name })
//     // console.log('result ==>', result)
//     if (!result.ok || !result.data) {
//       ElMessage.error(item.name + '上传失败')
//       return undefined
//     }

//     const cosClientAndParams = await getUploadCosSign(result.data)
//     // console.log('cosClientAndParams ==>', cosClientAndParams)
//     const uploadResult = await uploadImageCos(
//       cosClientAndParams.cos,
//       undefined,
//       item,
//       cosClientAndParams.params,
//       () => {}
//     )
//     // console.log('uploadResult ==>', uploadResult)

//     const params = {
//       fileName: item.name,
//       fileUrl: `https://aimaster-1256600262.cos.ap-shanghai.myqcloud.com/${uploadResult}`
//     }
//     const uploadByUrlResult = await uploadByUrl(params)
//     if (!uploadByUrlResult.ok) {
//       ElMessage.error(item.name + '上传失败')
//       return
//     }
//     // console.log('uploadByUrlResult ==>', uploadByUrlResult)
//     const uploadRes = {
//       fileId: uploadByUrlResult.data.id,
//       fileUrl: uploadByUrlResult.data.fileUrl
//     }
//     return uploadRes.fileUrl
//   } catch (error) {
//     ElMessage.error('文件上传失败，请重试')
//     throw error
//   }
// }
// export async function getUploadCosSign(data: CosSignResultInfo) {
//   const protocol = 'https:'

//   const params: CosUploadParams = {
//     bucket: data.bucket,
//     region: data.region,
//     protocol: protocol,
//     key: data.key
//   }
//   const cos = await new COS({
//     getAuthorization: function (_options: any, callback: (par: any) => void) {
//       // 异步获取临时密钥

//       const credentials = data.response.credentials

//       if (!data || !credentials) return console.error('获取cos签名错误 credentials invalid')
//       callback({
//         TmpSecretId: credentials.tmpSecretId,
//         TmpSecretKey: credentials.tmpSecretKey,
//         SecurityToken: credentials.sessionToken,
//         // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
//         StartTime: data.response.startTime, // 时间戳，单位秒，如：1580000000
//         ExpiredTime: data.response.expiredTime // 时间戳，单位秒，如：1580000900
//       })
//     }
//   })
//   return { cos: cos, params: params }
// }

/**
 *
 *上传文件
 *
 *
 */
// export function uploadImageCos(
//   cos: any,
//   localPath: string | undefined,
//   file: any,
//   params: CosUploadParams,
//   progress: Function
// ): Promise<string | undefined> {
//   return new Promise(function (resolve, reject) {
//     console.log('uploadImageCos begin localPath = ', localPath)
//     console.log('uploadImageCos begin file = ', file)
//     console.log('uploadImageCos begin params = ', params)

//     cos.putObject(
//       {
//         Bucket: params.bucket /* 必须 */,
//         Region: params.region /* 存储桶所在地域，必须字段 */,
//         Key: params.key /* 必须 */,
//         // StorageClass: 'STANDARD',
//         FilePath: localPath, // 上传文件对象
//         StorageClass: localPath ? undefined : 'STANDARD',
//         Body: file, // 上传文件对象
//         onProgress: function (progressData: any) {
//           console.log('onProgress progressData ==>', progressData)
//           progress({
//             progress: progressData.loaded / progressData.total,
//             totalBytesSent: progressData.loaded,
//             totalBytesExpectedToSend: progressData.total
//           })
//           if (progressData.total === progressData.loaded) {
//           }
//         }
//       },
//       function (err: any, data: any) {
//         console.log('putObject error', err || data)
//         // 上传成功之后
//         if (data && data.statusCode === 200) {
//           resolve(params.key)
//         } else {
//           reject(err)
//         }
//       }
//     )
//   })
// }

const saveUploadByUrl = async (fileName: string, fileUrl: string) => {
  const params = {
    fileName: fileName,
    fileUrl: fileUrl
  }
  const res = await uploadByUrl(params)
  if (!res.success) {
    ElMessage.error(fileName + '上传失败')
    return null
  }
  return res.data
}

export const removeQuestionMarkText = (str: string) => {
  return str.replace(/\?.*$/, '')
}

export const uploadFnOSS = async (file: any) => {
  try {
    const item = file
    const cosClientAndParams = await ossGetSign({ filename: item.name })
    const credentials = cosClientAndParams?.data?.response.credentials
    if (!credentials) {
      ElMessage.error('上传失败')
      return ''
    }
    const client = await getUploadAliossSign({
      ...credentials,
      region: cosClientAndParams?.data?.region,
      bucket: cosClientAndParams?.data?.bucket
    })

    const result = await client.multipartUpload(cosClientAndParams?.data?.key, file)
    if (result && result?.res?.status === 200) {
      if (Array.isArray(result.res.requestUrls) && result.res.requestUrls.length > 0) {
        const fileUrl = removeQuestionMarkText(result.res.requestUrls[0])

        const fileData = await saveUploadByUrl(file.name, fileUrl)

        const _response = { fileUrl, fileId: fileData.id, name: file.name, status: 'done' }
        file.url = fileUrl
        ElMessage.success('上传成功')
        return fileUrl
      }
    } else {
      ElMessage.error('文件上传失败，HTTP状态码:' + result.res.status)
    }
    return ''
  } catch (error) {
    ElMessage.error('文件上传失败，请重试')
    throw error
  }
}

export async function getUploadAliossSign(data: any) {
  const client = new OSS({
    accessKeyId: data.accessKeyId,
    accessKeySecret: data.accessKeySecret,
    stsToken: data.securityToken,
    bucket: data.bucket,
    region: data.region,
    refreshSTSToken: {
      accessKeyId: data.accessKeyId,
      accessKeySecret: data.accessKeySecret,
      stsToken: data.securityToken
    },
    refreshSTSTokenInterval: 300000,
    secure: true
  })
  return client
}

export const formatText2Html = (text: string) => {
  var html = text
  if (html) {
    html = html.replace(/ /g, '&nbsp;')
    html = html.replace(/\n/g, '<br/>')
  }
  return html
}

export function sleep(time: number) {
  return new Promise((resolve) => setTimeout(resolve, time))
}

export const isXiaoin = () => {
  return import.meta.env.VITE_APP_API_APP_NAME === 'xiaoin'
}
export function loadIcon(url: string) {
  var link = document.createElement('link')
  link.rel = 'icon'
  link.href = url
  document.head.appendChild(link)
}
export const getRewritingExpens = () => {
  if (isXiaoin()) {
    return rewritingExpense
  } else {
    return rewritingExpense / 1000
  }
}
export const getUnit = () => {
  if (isXiaoin()) {
    return '硬币'
  } else {
    return '算力'
  }
}
export function parentPostMessage(command: string, value = {}) {
  if (window.parent) {
    const postdata = {
      command,
      value
    }
    window.parent.postMessage(postdata, '*')
  }
}
export function downloadFile(url: string, fileName: string = '') {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  link.target = '_blank' // 可选，如果希望在新窗口中下载文件，请取消注释此行
  link.click()
}

// 格式化硬币数量的函数
export const formatCoinAmount = (amount: number, fixed: number = 2) => {
  // 不使用四舍五入的方式截取小数点后的位数
  const truncateDecimals = (num: number, digits: number) => {
    const multiplier = Math.pow(10, digits)
    return Math.floor(num * multiplier) / multiplier
  }

  if (amount >= 100000000) {
    // 大于等于一亿
    const billion = truncateDecimals(amount / 100000000, fixed)
    // 格式化数字，确保有指定位数的小数，并移除末尾的0
    // const formattedValue = billion.toFixed(fixed).replace(/\.?0+($| )/, '$1');
    const formattedValue = billion
      .toFixed(fixed)
      .replace(/\.([0-9]*?)0+$/, '.$1')
      .replace(/\.$/, '')

    return `${formattedValue}亿`
  } else if (amount >= 10000) {
    // 大于等于一万
    const tenThousand = truncateDecimals(amount / 10000, fixed)
    // 格式化数字，确保有指定位数的小数，并移除末尾的0
    // const formattedValue = tenThousand.toFixed(fixed).replace(/\.?0+($| )/, '$1');
    const formattedValue = tenThousand
      .toFixed(fixed)
      .replace(/\.([0-9]*?)0+$/, '.$1')
      .replace(/\.$/, '')

    return `${formattedValue}万`
  }
  // 小于一万直接返回整数
  return `${Math.floor(amount)}`
}

export const isJSON = (str: unknown) => {
  if (typeof str !== 'string') {
    return false
  }
  try {
    const result = JSON.parse(str)
    const type = Object.prototype.toString.call(result)
    return type === '[object Object]' || type === '[object Array]'
  } catch (err) {
    return false
  }
}

// 清理JSON字符串中的控制字符
export const cleanJsonString = (jsonStr: string): string => {
  if (!jsonStr) return jsonStr

  // 移除markdown代码块标记
  let cleaned = jsonStr.replace(/```json\n|\n```/g, '')

  // 清理常见的控制字符，但保留必要的转义字符
  cleaned = cleaned
    // 移除不可见的控制字符（除了换行、制表符、回车）
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // 修复可能的引号问题
    .replace(/([^\\])"/g, '$1\\"') // 转义未转义的引号
    .replace(/^"/, '\\"') // 处理开头的引号
    // 清理多余的空白字符
    .replace(/\s+/g, ' ')
    .trim()

  return cleaned
}

// 安全的JSON解析函数
export const safeJsonParse = (jsonStr: string): any => {
  try {
    // 首先尝试直接解析
    return JSON.parse(jsonStr)
  } catch (error) {
    console.warn('直接JSON解析失败，尝试清理后解析:', error)

    try {
      // 清理后再次尝试解析
      const cleaned = cleanJsonString(jsonStr)
      return JSON.parse(cleaned)
    } catch (cleanError) {
      console.error('清理后JSON解析仍然失败:', cleanError)
      console.error('原始字符串:', jsonStr)
      const errorMessage = cleanError instanceof Error ? cleanError.message : String(cleanError)
      throw new Error(`JSON解析失败: ${errorMessage}`)
    }
  }
}
