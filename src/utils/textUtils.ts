interface TextElementResult {
  valid: boolean
  type: string
  index: number
  subtype: string
}

/**
 * 识别SVG文本元素的类型和索引
 * @param id 文本元素的ID
 * @param textContent 文本元素的内容
 * @returns 包含识别结果的对象
 */
export const identifyTextElement = (id: string, textContent: string): TextElementResult => {
  const result: TextElementResult = { valid: false, type: '', index: -1, subtype: '' }

  // 检查ID是否存在
  if (!id && !textContent) return result

  // 尝试从ID识别
  if (id) {
    // 匹配描述文本模式: tx-XX-N-desc
    const descMatch = id.match(/tx-([^-]+)-(\d+)-desc/)
    // console.log('从ID识别:', id, descMatch)
    if (descMatch) {
      result.valid = true
      result.type = 'desc'
      result.subtype = descMatch[1] // 例如 lt, ct, rt
      result.index = parseInt(descMatch[2]) - 1
      return result
    }

    // 匹配标题文本模式: tx-XX-N
    const titleMatch = id.match(/tx-([^-]+)-(\d+)$/)
    // console.log('从ID识别 titleMatch:', id, titleMatch)
    if (titleMatch) {
      result.valid = true
      result.type = 'name'
      result.subtype = titleMatch[1] // 例如 lt, ct, rt
      result.index = parseInt(titleMatch[2]) - 1
      return result
    }
  }

  // 从内容中尝试识别
  if (textContent) {
    // 尝试识别描述文本
    if (textContent.includes('desc')) {
      const match = textContent.match(/tx-[^-]+-(\d+)-desc/)
      if (match && match[1]) {
        result.valid = true
        result.type = 'desc'
        result.index = parseInt(match[1]) - 1
        return result
      }
    }

    // 尝试识别标题文本 - 包括caption格式
    const captionMatch = textContent.match(/tx-[^-]+-(\d+)-caption/)
    if (captionMatch && captionMatch[1]) {
      result.valid = true
      result.type = 'name'
      result.index = parseInt(captionMatch[1]) - 1
      return result
    }

    // 尝试识别普通标题文本
    const match = textContent.match(/tx-[^-]+-(\d+)$/)
    if (match && match[1]) {
      result.valid = true
      result.type = 'name'
      result.index = parseInt(match[1]) - 1
      return result
    }
  }

  return result
}
