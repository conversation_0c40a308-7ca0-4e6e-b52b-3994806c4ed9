
export const storage = {
  get(key: string, optionalDefaultValue?: any): any {
    const ret = localStorage.getItem(key)
    if (!ret && optionalDefaultValue) {
      return optionalDefaultValue
    }
    if(ret){
      try {
        const obj = JSON.parse(ret)
        return obj?.data ?? ''
      } catch (error) {
        return ''
      }
    }
    return ''
  },
  set(key: string, value: any): any {
    const _val = JSON.stringify({data: value})
    localStorage.setItem(key, _val)
  },
  remove(key: string): void {
    localStorage.removeItem(key)
  }
}
