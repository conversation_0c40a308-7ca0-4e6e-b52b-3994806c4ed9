export const getBspId = (): number => {
  return 1
}
export const isLocalHost = (): boolean => {
  const host = window.location.host
  const regLocalHost =
    /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$/
  if (regLocalHost.test(host) || host.indexOf('localhost') > -1) {
    return true
  }
  return false
}
