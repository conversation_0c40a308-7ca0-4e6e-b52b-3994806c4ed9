/**
 * 参考文献处理工具函数
 * 提供参考文献提取、识别、替换等功能
 */

// 检查段落是否为参考文献标识
export const isReferenceIdentifier = (text: string): boolean => {
  const trimmedText = text.trim()

  // 检查是否是参考文献标识段落
  const referenceIdentifiers = [
    /^参考文献\s*$/i,
    /^references\s*$/i,
    /^bibliography\s*$/i,
    /^参考文献\s*[:：]\s*$/i,
    /^references\s*[:：]\s*$/i,
    /^bibliography\s*[:：]\s*$/i,
    /^文献\s*$/i,
    /^reference\s*$/i,
    // 添加更多可能的模式
    /^参考文献列表\s*$/i,
    /^reference\s+list\s*$/i,
    /^参考资料\s*$/i,
    /^引用文献\s*$/i,
    /^cited\s+references\s*$/i
  ]

  return referenceIdentifiers.some((pattern) => pattern.test(trimmedText))
}

// 改进的参考文献提取函数（支持段落级别的标识）
export const extractReferences = (editor: any) => {
  if (!editor) {
    return { references: [], citations: [] }
  }

  // 检查编辑器状态
  if (!editor.state || !editor.state.doc) {
    return { references: [], citations: [] }
  }

  const references: any[] = []
  const citations: any[] = []
  let isInReferenceSection = false

  // 遍历文档内容
  editor.state.doc.descendants((node: any, pos: any) => {
    // 检查是否是参考文献标题 - 改进的匹配逻辑
    if (node.type.name === 'heading') {
      const headingText = node.textContent || ''

      // 更宽松的标题匹配
      const isReferenceHeading =
        headingText.includes('参考文献') ||
        headingText.includes('References') ||
        headingText.includes('Bibliography') ||
        headingText.toLowerCase().includes('reference') ||
        headingText.includes('文献') ||
        /参考|reference|bibliography/i.test(headingText)

      if (isReferenceHeading) {
        isInReferenceSection = true
        return true
      } else if (isInReferenceSection && headingText.trim()) {
        // 遇到其他标题，结束参考文献区域

        isInReferenceSection = false
      }
    }

    // 检查段落是否为参考文献标识（新增逻辑）
    if (node.type.name === 'paragraph' && !isInReferenceSection) {
      const text = node.textContent || ''

      if (isReferenceIdentifier(text)) {
        isInReferenceSection = true
        return true // 继续遍历后续内容
      }
    }

    // 在参考文献区域内收集参考文献条目 - 改进的匹配逻辑
    if (
      isInReferenceSection &&
      (node.type.name === 'paragraph' || node.type.name === 'ReferenceItem')
    ) {
      const text = node.textContent || ''

      // 跳过参考文献标识段落本身
      if (isReferenceIdentifier(text)) {
        return true
      }

      const hasReferenceClass = node.attrs?.class?.includes('reference-item')
      const isReferenceItemNode = node.type.name === 'ReferenceItem'

      // 更宽松的参考文献格式检查
      const patterns = [
        /^\s*\[?\d+\]?\s*[^\s]/, // [1] 或 1. 开头
        /^\s*\d+\.\s*/, // 数字加点
        /^\s*\[\d+\]\s*/, // 方括号数字
        /^\s*\d+\s+/, // 纯数字开头
        /^[A-Za-z].*\d{4}/, // 作者年份格式
        /.+\.\s*$/ // 以句号结尾的文本
      ]

      const matchesPattern = patterns.some((pattern) => pattern.test(text))

      // 如果是 ReferenceItem 节点，直接添加；否则按原逻辑判断
      if (isReferenceItemNode || (text.trim() && text.length > 5)) {
        references.push({
          text: text.trim(),
          position: pos,
          hasClass: hasReferenceClass,
          matchesPattern,
          nodeType: node.type.name
        })
      }
    }

    // 收集文档中的引用标记
    if (!isInReferenceSection && node.type.name === 'paragraph') {
      const text = node.textContent || ''
      const citationPattern = /\[(\d+(?:[-,]\d+)*)\]/g
      let match
      while ((match = citationPattern.exec(text)) !== null) {
        citations.push({
          citation: match[0],
          numbers: match[1],
          position: pos + match.index,
          context: text.substring(
            Math.max(0, match.index - 20),
            Math.min(text.length, match.index + match[0].length + 20)
          )
        })
      }
    }

    return true
  })

  if (references.length === 0) {
    console.warn('未找到参考文献内容')
  }

  return { references, citations }
}

// 带重试机制的参考文献提取函数
export const extractReferencesWithRetry = async (
  editor: any,
  maxRetries: number = 3
): Promise<{ references: any[]; citations: any[] }> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = extractReferences(editor)

      if (result.references.length > 0) {
        return result
      } else {
        if (attempt < maxRetries) {
          // 等待编辑器状态稳定
          const delay = Math.pow(2, attempt - 1) * 100 // 指数退避: 100ms, 200ms, 400ms
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }
    } catch (error) {
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt - 1) * 200 // 错误时更长的等待时间
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }
  }

  return { references: [], citations: [] }
}

// DOM 查询备选检测方法
export const extractReferencesByDOM = (editor: any): { references: any[]; citations: any[] } => {
  if (!editor || !editor.view || !editor.view.dom) {
    return { references: [], citations: [] }
  }

  const references: any[] = []
  const citations: any[] = []
  const editorDOM = editor.view.dom

  try {
    // 增强的参考文献标题查找策略
    let referenceHeading: Element | null = null

    // 策略1: 查找标题标签 (h1-h6)
    const headings = editorDOM.querySelectorAll('h1, h2, h3, h4, h5, h6')

    for (const heading of headings) {
      const text = heading.textContent?.trim() || ''

      if (
        text.includes('参考文献') ||
        text.includes('References') ||
        text.includes('Bibliography') ||
        text.toLowerCase().includes('reference') ||
        text.includes('文献')
      ) {
        referenceHeading = heading
        break
      }
    }

    // 策略2: 查找目录项 (li标签)
    if (!referenceHeading) {
      const listItems = editorDOM.querySelectorAll('li')
      for (const li of listItems) {
        const text = li.textContent?.trim() || ''

        if (
          text.includes('参考文献') ||
          text.includes('References') ||
          text.includes('Bibliography') ||
          text.toLowerCase().includes('reference') ||
          text.includes('文献')
        ) {
          referenceHeading = li
          break
        }
      }
    }

    // 策略3: 查找参考文献段落标识
    if (!referenceHeading) {
      const paragraphs = editorDOM.querySelectorAll('p')
      for (const p of paragraphs) {
        const text = p.textContent?.trim() || ''
        if (text && isReferenceIdentifier(text)) {
          referenceHeading = p
          break
        }
      }
    }

    // 策略4: 通过data-toc-id或其他属性查找
    if (!referenceHeading) {
      const elementsWithTocId = editorDOM.querySelectorAll('[data-toc-id]')
      for (const element of elementsWithTocId) {
        const text = element.textContent?.trim() || ''

        if (
          text.includes('参考文献') ||
          text.includes('References') ||
          text.includes('Bibliography') ||
          text.toLowerCase().includes('reference') ||
          text.includes('文献')
        ) {
          referenceHeading = element
          break
        }
      }
    }

    if (referenceHeading) {
      // 智能内容收集策略
      const collectReferences = (startElement: Element) => {
        let refIndex = 0

        // 方法1: 收集兄弟元素中的参考文献
        let currentElement = startElement.nextElementSibling

        while (currentElement) {
          const tagName = currentElement.tagName.toLowerCase()

          // 遇到下一个标题，停止收集
          if (
            tagName.startsWith('h') ||
            (tagName === 'li' && currentElement.textContent?.includes('参考文献') === false)
          ) {
            const text = currentElement.textContent?.trim() || ''
            if (text && !text.includes('参考文献') && !text.includes('References')) {
              break
            }
          }

          // 收集段落内容
          if (tagName === 'p' || tagName === 'div') {
            const text = currentElement.textContent?.trim() || ''
            if (text && text.length > 5 && !isReferenceIdentifier(text)) {
              refIndex++
              references.push({
                text: text.trim(),
                position: -1,
                hasClass: currentElement.classList.contains('reference-item'),
                matchesPattern: true,
                nodeType: tagName,
                source: 'DOM-sibling'
              })
            }
          }

          // 检查嵌套的列表项
          if (tagName === 'ul' || tagName === 'ol') {
            const listItems = currentElement.querySelectorAll('li')
            for (const li of listItems) {
              const text = li.textContent?.trim() || ''
              if (text && text.length > 5 && !isReferenceIdentifier(text)) {
                refIndex++
                references.push({
                  text: text.trim(),
                  position: -1,
                  hasClass: li.classList.contains('reference-item'),
                  matchesPattern: true,
                  nodeType: 'list-item',
                  source: 'DOM-list'
                })
              }
            }
          }

          currentElement = currentElement.nextElementSibling
        }

        // 方法2: 如果没有找到兄弟元素中的内容，尝试查找父容器中的内容
        if (refIndex === 0) {
          const parentElement = startElement.parentElement
          if (parentElement) {
            const allParagraphs = parentElement.querySelectorAll('p, div')
            let foundStart = false

            for (const p of allParagraphs) {
              const text = p.textContent?.trim() || ''

              // 找到参考文献标题后开始收集
              if (!foundStart && (text.includes('参考文献') || text.includes('References'))) {
                foundStart = true
                continue
              }

              if (foundStart && text && text.length > 5 && !isReferenceIdentifier(text)) {
                refIndex++
                references.push({
                  text: text.trim(),
                  position: -1,
                  hasClass: p.classList.contains('reference-item'),
                  matchesPattern: true,
                  nodeType: p.tagName.toLowerCase(),
                  source: 'DOM-parent'
                })
              }
            }
          }
        }

        return refIndex
      }

      collectReferences(referenceHeading)
    }
    return { references, citations }
  } catch (error) {
    return { references: [], citations: [] }
  }
}

// 专门针对特定DOM结构的增强检测方法
export const extractReferencesBySpecificDOM = (
  editor: any
): { references: any[]; citations: any[] } => {
  if (!editor || !editor.view || !editor.view.dom) {
    return { references: [], citations: [] }
  }

  const references: any[] = []
  const citations: any[] = []
  const editorDOM = editor.view.dom

  try {
    // 特定策略1: 查找目录中的参考文献项
    const tocItems = editorDOM.querySelectorAll('li.toc-entity, li[class*="toc"]')
    let referenceHeadingId: string | null = null

    for (const tocItem of tocItems) {
      const text = tocItem.textContent?.trim() || ''

      if (text.includes('参考文献') || text.includes('References')) {
        // 尝试从目录项中获取对应的标题ID
        const linkElement = tocItem.querySelector('a[href^="#"]')
        if (linkElement) {
          const href = linkElement.getAttribute('href')
          if (href) {
            referenceHeadingId = href.substring(1) // 移除 # 号
            break
          }
        }
      }
    }

    // 特定策略2: 通过ID查找对应的标题元素
    let referenceHeading: Element | null = null
    if (referenceHeadingId) {
      referenceHeading = editorDOM.querySelector(`#${referenceHeadingId}`)
    }

    // 特定策略3: 直接查找带有data-toc-id的参考文献标题
    if (!referenceHeading) {
      const elementsWithTocId = editorDOM.querySelectorAll('[data-toc-id]')
      for (const element of elementsWithTocId) {
        const text = element.textContent?.trim() || ''
        if (text.includes('参考文献') || text.includes('References')) {
          referenceHeading = element
          break
        }
      }
    }

    // 收集参考文献内容
    if (referenceHeading) {
      // 查找参考文献标题后的所有段落
      let currentElement = referenceHeading.nextElementSibling

      while (currentElement) {
        const tagName = currentElement.tagName.toLowerCase()
        const text = currentElement.textContent?.trim() || ''

        // 遇到下一个标题，停止收集
        if (tagName.startsWith('h') && text && !text.includes('参考文献')) {
          break
        }

        // 收集段落内容
        if ((tagName === 'p' || tagName === 'div') && text && text.length > 10) {
          // 检查是否是参考文献条目
          const isReference =
            /^\s*\[?\d+\]?\s*/.test(text) || // 数字开头
            /^\s*\d+\.\s*/.test(text) || // 数字加点
            text.includes('，') || // 包含中文逗号
            text.includes('．') || // 包含中文句号
            text.length > 20 // 长度足够

          if (isReference) {
            references.push({
              text: text.trim(),
              position: -1,
              hasClass: currentElement.classList.contains('reference-item'),
              matchesPattern: true,
              nodeType: tagName,
              source: 'DOM-specific'
            })
          }
        }

        currentElement = currentElement.nextElementSibling
      }
    }

    return { references, citations }
  } catch (error) {
    return { references: [], citations: [] }
  }
}

// 编辑器状态稳定性检测
export const checkEditorStability = async (editor: any): Promise<boolean> => {
  if (!editor || !editor.view || !editor.state) {
    return false
  }

  try {
    // 检查编辑器是否正在更新
    const isUpdating = editor.view.updating || editor.state.applying
    if (isUpdating) {
      return false
    }

    // 检查DOM和ProseMirror文档的一致性
    const domText = editor.view.dom.textContent || ''
    const docText = editor.state.doc.textBetween(0, editor.state.doc.content.size)

    if (domText.length !== docText.length) {
      return false
    }

    // 检查是否有pending的事务
    if (editor.state.tr && editor.state.tr.steps.length > 0) {
      return false
    }

    return true
  } catch (error) {
    console.error('编辑器状态检测出错:', error)
    return false
  }
}

// 等待编辑器状态稳定
export const waitForEditorStability = async (
  editor: any,
  maxWaitTime: number = 3000
): Promise<boolean> => {
  const startTime = Date.now()
  const checkInterval = 100 // 每100ms检查一次

  while (Date.now() - startTime < maxWaitTime) {
    const isStable = await checkEditorStability(editor)
    if (isStable) {
      return true
    }

    // 等待一段时间后再次检查
    await new Promise((resolve) => setTimeout(resolve, checkInterval))
  }

  return false
}

// 多编辑器实例检测和管理
export const detectAllEditorInstances = (): any[] => {
  const editorInstances: any[] = []

  try {
    // 尝试从全局作用域获取编辑器实例
    if (typeof window !== 'undefined') {
      // 检查Vue组件实例中的编辑器
      const vueApp = (window as any).__VUE_APP__
      if (vueApp) {
        // 这里可以添加更多的Vue实例检测逻辑
      }
    }

    // 检查DOM中的编辑器元素
    const editorElements = document.querySelectorAll(
      '[class*="ProseMirror"], [class*="tiptap"], .editor-content'
    )

    editorElements.forEach((element: any, index: number) => {
      if (element && element.textContent) {
        const textLength = element.textContent.length
        const hasReferenceKeyword =
          element.textContent.includes('参考文献') || element.textContent.includes('References')

        if (hasReferenceKeyword) {
          editorInstances.push({
            type: 'dom-element',
            element: element,
            textLength: textLength,
            hasReferences: true,
            index: index
          })
        }
      }
    })

    return editorInstances
  } catch (error) {
    return []
  }
}

// 智能选择最佳编辑器实例
export const selectBestEditorInstance = (primaryEditor: any): any => {
  // 首先检查主编辑器是否可用且包含参考文献
  if (primaryEditor && primaryEditor.view && primaryEditor.state) {
    try {
      const docText = primaryEditor.state.doc.textBetween(0, primaryEditor.state.doc.content.size)
      const hasReferences = docText.includes('参考文献') || docText.includes('References')

      if (hasReferences) {
        return primaryEditor
      }
    } catch (error) {
      // 忽略错误，继续使用其他方法
    }
  }

  // 如果主编辑器不可用或不包含参考文献，检测其他实例
  const allInstances = detectAllEditorInstances()

  if (allInstances.length > 0) {
    // 选择文本长度最大的实例（通常是主内容编辑器）
    const bestInstance = allInstances.reduce((best, current) => {
      return current.textLength > best.textLength ? current : best
    })

    // 尝试从DOM元素构造一个伪编辑器对象
    return {
      view: {
        dom: bestInstance.element
      },
      state: {
        doc: {
          content: { size: bestInstance.textLength },
          textBetween: () => bestInstance.element.textContent || ''
        }
      },
      _isFallbackEditor: true
    }
  }

  return primaryEditor
}

// 综合的参考文献提取函数（结合所有方法）
export const extractReferencesRobust = async (
  editor: any
): Promise<{ references: any[]; citations: any[] }> => {
  // 第一步：等待编辑器状态稳定
  await waitForEditorStability(editor, 3000)

  // 第二步：智能选择最佳编辑器实例
  const bestEditor = selectBestEditorInstance(editor)

  // 第三步：执行多种检测方法

  // 方法1: 特定DOM结构检测
  const specificDomResult = extractReferencesBySpecificDOM(bestEditor)
  if (specificDomResult.references.length > 0) {
    return specificDomResult
  }

  // 方法2: 增强的DOM查询方法
  const domResult = extractReferencesByDOM(bestEditor)
  if (domResult.references.length > 0) {
    return domResult
  }

  // 方法3: 带重试的标准提取
  const standardResult = await extractReferencesWithRetry(bestEditor, 2)
  if (standardResult.references.length > 0) {
    return standardResult
  }

  // 方法4: 最后尝试直接调用一次（无重试）
  const finalResult = extractReferences(bestEditor)
  if (finalResult.references.length > 0) {
    return finalResult
  }

  // 方法5: ProseMirror文档结构检测
  const proseMirrorResult = extractReferencesByProseMirrorDoc(bestEditor)
  if (proseMirrorResult.references.length > 0) {
    return proseMirrorResult
  }

  // 方法6: 全文搜索兜底方法
  const fullTextResult = extractReferencesByFullTextSearch(bestEditor)
  if (fullTextResult.references.length > 0) {
    return fullTextResult
  }

  // 所有方法都失败了
  console.error('未找到参考文献内容')
  return { references: [], citations: [] }
}
// 基于ProseMirror文档结构的检测方法
export const extractReferencesByProseMirrorDoc = (
  editor: any
): { references: any[]; citations: any[] } => {
  if (!editor || !editor.state || !editor.state.doc) {
    return { references: [], citations: [] }
  }

  const references: any[] = []
  const citations: any[] = []
  const doc = editor.state.doc

  try {
    // 遍历文档的所有节点
    let foundReferenceSection = false
    let referenceStartPos = -1

    doc.descendants((node: any, pos: number) => {
      const text = node.textContent || ''
      const nodeType = node.type.name

      // 查找参考文献标题
      if ((!foundReferenceSection && text.includes('参考文献')) || text.includes('References')) {
        foundReferenceSection = true
        referenceStartPos = pos
        return true // 继续遍历
      }

      // 如果已经找到参考文献标题，开始收集后续的参考文献条目
      if (foundReferenceSection && pos > referenceStartPos) {
        if (nodeType === 'paragraph' || nodeType === 'text') {
          const trimmedText = text.trim()

          // 检查是否是参考文献条目
          if (trimmedText.length > 20 && isReferenceIdentifier(trimmedText)) {
            references.push({
              text: trimmedText,
              position: pos,
              hasClass: false,
              matchesPattern: true,
              nodeType: nodeType,
              source: 'prosemirror-doc'
            })
          }
        }
      }

      return true // 继续遍历
    })

    return { references, citations }
  } catch (error) {
    return { references: [], citations: [] }
  }
}

// 全文搜索兜底方法（最后的备选方案）
export const extractReferencesByFullTextSearch = (
  editor: any
): { references: any[]; citations: any[] } => {
  if (!editor || !editor.view || !editor.view.dom) {
    return { references: [], citations: [] }
  }

  const references: any[] = []
  const citations: any[] = []
  const editorDOM = editor.view.dom

  try {
    const fullText = editorDOM.textContent || ''

    // 查找参考文献关键词的位置
    const keywords = ['参考文献', 'References', 'Bibliography']
    let referenceStartIndex = -1
    let foundKeyword = ''

    for (const keyword of keywords) {
      const index = fullText.indexOf(keyword)
      if (index !== -1) {
        referenceStartIndex = index
        foundKeyword = keyword
        break
      }
    }

    if (referenceStartIndex === -1) {
      return { references: [], citations: [] }
    }

    // 从关键词位置开始，查找后续的参考文献条目
    const textAfterKeyword = fullText.substring(referenceStartIndex + foundKeyword.length)

    // 使用正则表达式匹配参考文献条目
    const referencePatterns = [
      /\[?\d+\]?\s*[^\n]{20,}/g, // [1] 或 1 开头的长文本
      /\d+\.\s*[^\n]{20,}/g, // 数字加点开头的长文本
      /[A-Za-z][^.\n]*\.\s*[^\n]{20,}/g // 作者格式的文本
    ]

    let foundReferences = 0
    for (const pattern of referencePatterns) {
      const matches = textAfterKeyword.match(pattern)
      if (matches) {
        matches.forEach((match: string) => {
          const text = match.trim()
          if (text.length > 20 && !text.includes(foundKeyword)) {
            foundReferences++
            references.push({
              text: text,
              position: -1,
              hasClass: false,
              matchesPattern: true,
              nodeType: 'text',
              source: 'fulltext-search'
            })
          }
        })

        if (foundReferences > 0) {
          break // 找到参考文献就停止尝试其他模式
        }
      }
    }

    return { references, citations }
  } catch (error) {
    return { references: [], citations: [] }
  }
}

// 提取完整的参考文献区域（改进版）
export const extractReferenceRegion = (editor: any) => {
  if (!editor) {
    return { found: false, from: -1, to: -1, content: '', headingPos: -1 }
  }

  let referenceHeadingPos = -1
  let referenceRegionStart = -1
  let referenceRegionEnd = -1
  let isInReferenceSection = false
  let regionContent = ''

  // 遍历文档查找参考文献区域
  editor.state.doc.descendants((node: any, pos: any) => {
    if (node.type.name === 'heading') {
      const headingText = node.textContent || ''

      // 使用改进的标题匹配逻辑
      const isReferenceHeading =
        headingText.includes('参考文献') ||
        headingText.includes('References') ||
        headingText.includes('Bibliography') ||
        headingText.toLowerCase().includes('reference') ||
        headingText.includes('文献') ||
        /参考|reference|bibliography/i.test(headingText)

      if (isReferenceHeading) {
        // 找到参考文献标题
        referenceHeadingPos = pos
        referenceRegionStart = pos + node.nodeSize
        isInReferenceSection = true
        return true
      } else if (isInReferenceSection && headingText.trim()) {
        // 遇到下一个标题，结束参考文献区域
        referenceRegionEnd = pos
        isInReferenceSection = false
        return false // 停止遍历
      }
    }

    // 检查段落是否为参考文献标识（新增逻辑）
    if (node.type.name === 'paragraph' && !isInReferenceSection) {
      const text = node.textContent || ''

      if (isReferenceIdentifier(text)) {
        referenceHeadingPos = pos
        referenceRegionStart = pos + node.nodeSize
        isInReferenceSection = true
        return true
      }
    }

    // 收集参考文献区域内的内容
    if (
      isInReferenceSection &&
      (node.type.name === 'paragraph' || node.type.name === 'ReferenceItem')
    ) {
      const text = node.textContent || ''

      // 跳过参考文献标识段落本身
      if (!isReferenceIdentifier(text) && text.trim()) {
        regionContent += text.trim() + '\n'
      }
    }

    return true
  })

  // 如果没有找到结束位置，使用文档末尾
  if (isInReferenceSection && referenceRegionEnd === -1) {
    referenceRegionEnd = editor.state.doc.content.size
  }

  const found =
    referenceHeadingPos !== -1 && referenceRegionStart !== -1 && referenceRegionEnd !== -1

  return {
    found,
    from: referenceRegionStart,
    to: referenceRegionEnd,
    content: regionContent.trim(),
    headingPos: referenceHeadingPos
  }
}

// 验证格式化后的内容
export const validateFormattedContent = (formattedContent: string) => {
  if (!formattedContent || !formattedContent.trim()) {
    return { valid: false, reason: '格式化内容为空' }
  }

  const formattedLines = formattedContent.split('\n').filter((line) => line.trim())
  console.log('formattedLines ==>', formattedLines)
  // 检查格式化后的内容是否合理（至少有一条文献）
  if (formattedLines.length === 0) {
    return { valid: false, reason: '格式化后没有有效的文献条目' }
  }

  // 可以添加更多验证规则，比如检查文献格式等
  return { valid: true, reason: '' }
}

// 区域替换实现
export const replaceReferenceRegion = async (
  editor: any,
  region: any,
  formattedContent: string
) => {
  try {
    if (!editor) {
      throw new Error('编辑器未初始化')
    }

    // 验证格式化内容
    const validation = validateFormattedContent(formattedContent)
    if (!validation.valid) {
      throw new Error(`内容验证失败: ${validation.reason}`)
    }

    // 使用更简单的替换策略：先删除，再插入
    editor.commands.deleteRange({ from: region.from, to: region.to })

    // 创建格式化后的内容
    const formattedLines = formattedContent.split('\n').filter((line) => line.trim())
    const contentToInsert = formattedLines.map((line) => ({
      type: 'paragraph',
      content: [{ type: 'text', text: line.trim() }]
    }))

    // 插入新内容
    editor.commands.insertContentAt(region.from, contentToInsert, {
      updateSelection: false,
      parseOptions: {
        preserveWhitespace: 'full'
      }
    })

    return true
  } catch (error) {
    console.error('区域替换失败:', error)
    throw error
  }
}

// 查找文献项在文档中的确切范围
export const findReferenceRange = (editor: any, reference: any) => {
  if (!editor) {
    return { from: -1, to: -1 }
  }

  let targetFrom = -1
  let targetTo = -1

  // 遍历文档查找匹配的文献项
  editor.state.doc.descendants((node: any, pos: any) => {
    if (node.type.name === 'paragraph') {
      const nodeText = node.textContent || ''

      // 检查是否是目标文献项（通过文本内容匹配）
      if (nodeText.trim() === reference.text.trim()) {
        targetFrom = pos
        targetTo = pos + node.nodeSize
        return false // 停止遍历
      }
    }
    return true
  })

  return { from: targetFrom, to: targetTo }
}

// 逐条替换实现（原有逻辑的优化版）
export const replaceReferencesIndividually = async (
  editor: any,
  originalReferences: any[],
  formattedContent: string
) => {
  try {
    if (!editor) {
      throw new Error('编辑器未初始化')
    }

    // 将格式化后的内容按行分割，过滤空行
    const formattedLines = formattedContent.split('\n').filter((line) => line.trim())

    if (formattedLines.length === 0) {
      throw new Error('格式化后的文献内容为空')
    }

    // 按位置从后往前排序，避免替换时位置偏移问题
    const sortedReferences = [...originalReferences].sort((a, b) => b.position - a.position)

    // 遍历每个文献项进行替换
    for (let i = 0; i < sortedReferences.length && i < formattedLines.length; i++) {
      const reference = sortedReferences[i]
      const formattedLine = formattedLines[i]

      // 查找当前文献项在文档中的确切位置和长度
      const { from, to } = findReferenceRange(editor, reference)

      if (from !== -1 && to !== -1) {
        // 创建新的段落节点
        const newParagraph = {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: formattedLine.trim()
            }
          ]
        }

        // 替换内容
        editor.commands.deleteRange({ from, to })
        editor.commands.insertContentAt(from, newParagraph, {
          updateSelection: false,
          parseOptions: {
            preserveWhitespace: 'full'
          }
        })
      }
    }

    // 如果格式化后的文献数量多于原文献数量，在最后添加剩余的文献
    if (formattedLines.length > originalReferences.length) {
      const lastReference = sortedReferences[sortedReferences.length - 1]
      if (lastReference) {
        const { to } = findReferenceRange(editor, lastReference)

        for (let i = originalReferences.length; i < formattedLines.length; i++) {
          const additionalLine = formattedLines[i]
          const newParagraph = {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: additionalLine.trim()
              }
            ]
          }

          editor.commands.insertContentAt(to + 1, newParagraph, {
            updateSelection: false,
            parseOptions: {
              preserveWhitespace: 'full'
            }
          })
        }
      }
    }
  } catch (error) {
    console.error('逐条替换失败:', error)
    throw error
  }
}

// 替换文献内容函数（重构版）
export const replaceReferencesContent = async (
  editor: any,
  originalReferences: any[],
  formattedContent: string
) => {
  try {
    if (!editor || !formattedContent) {
      throw new Error('编辑器未初始化或格式化内容为空')
    }

    // 策略1：尝试区域替换（优先策略）
    try {
      const region = extractReferenceRegion(editor)

      if (region.found && region.content) {
        await replaceReferenceRegion(editor, region, formattedContent)
        return
      }
    } catch (regionError) {
      console.warn('区域替换策略失败，回退到逐条替换策略:', regionError)
    }

    // 策略2：逐条替换（备选策略）
    await replaceReferencesIndividually(editor, originalReferences, formattedContent)
  } catch (error) {
    console.error('替换文献内容时出错:', error)
    throw error
  }
}

// 滚动到参考文献区域
export const scrollToReferenceSection = async (editor: any): Promise<boolean> => {
  try {
    if (!editor) {
      return false
    }

    // 首先尝试定位参考文献区域
    const region = extractReferenceRegion(editor)

    if (region.found && region.headingPos !== -1) {
      // 找到参考文献标题，滚动到标题位置
      return await scrollToPosition(editor, region.headingPos, '参考文献标题')
    } else {
      // 没有找到明确的标题，尝试滚动到第一个参考文献条目
      const { references } = extractReferences(editor)

      if (references.length > 0) {
        const firstReference = references[0]
        return await scrollToPosition(editor, firstReference.position, '第一个参考文献条目')
      }
    }

    return false
  } catch (error) {
    return false
  }
}

// 滚动到指定位置
const scrollToPosition = async (
  editor: any,
  position: number,
  description: string
): Promise<boolean> => {
  try {
    // 获取编辑器的 DOM 元素
    const editorElement = editor.view.dom
    if (!editorElement) {
      return false
    }

    // 尝试多种方法定位目标元素
    let targetElement: HTMLElement | null = null

    // 方法1: 使用 domAtPos 定位
    const domAtPos = editor.view.domAtPos(position)
    if (domAtPos && domAtPos.node) {
      let candidate = domAtPos.node

      // 如果是文本节点，获取其父元素
      if (candidate.nodeType === Node.TEXT_NODE) {
        candidate = candidate.parentElement
      }

      // 检查是否是编辑器根容器，如果是则说明定位失败
      if (candidate && candidate !== editorElement) {
        targetElement = candidate as HTMLElement
      }
    }

    // 方法2: 如果 domAtPos 失败，使用文本内容匹配（备选方案）
    if (!targetElement) {
      targetElement = findElementByTextContent(editorElement, description)
    }

    if (!targetElement) {
      return false
    }

    // 尝试找到更合适的滚动目标（段落或标题元素）
    const scrollTarget = findBestScrollTarget(targetElement, editorElement)

    // 计算滚动位置，添加一些偏移量避免被页面顶部遮挡
    const rect = scrollTarget.getBoundingClientRect()
    const scrollContainer = findScrollContainer(editorElement)

    // 动态计算偏移量
    const dynamicOffset = calculateDynamicOffset()

    if (scrollContainer) {
      const containerRect = scrollContainer.getBoundingClientRect()

      // 计算目标滚动位置
      const targetScrollTop =
        scrollContainer.scrollTop + rect.top - containerRect.top - dynamicOffset
      const finalScrollTop = Math.max(
        0,
        Math.min(targetScrollTop, scrollContainer.scrollHeight - scrollContainer.clientHeight)
      )

      // 执行平滑滚动
      scrollContainer.scrollTo({
        top: finalScrollTop,
        behavior: 'smooth'
      })

      // 可选：添加短暂的高亮效果
      await highlightElement(scrollTarget)

      return true
    } else {
      // 如果没有找到滚动容器，使用 window 滚动
      const windowScrollTop = window.pageYOffset + rect.top - dynamicOffset
      const finalScrollTop = Math.max(0, windowScrollTop)

      window.scrollTo({
        top: finalScrollTop,
        behavior: 'smooth'
      })

      // 可选：添加短暂的高亮效果
      await highlightElement(scrollTarget)

      return true
    }
  } catch (error) {
    console.error(`滚动到${description}时出错:`, error)
    return false
  }
}

// 动态计算偏移量
const calculateDynamicOffset = (): number => {
  try {
    // 检查页面顶部是否有固定定位的元素（如导航栏）
    const fixedElements = Array.from(document.querySelectorAll('*')).filter((el) => {
      const style = window.getComputedStyle(el)
      const isFixed = style.position === 'fixed'
      const isAtTop = style.top === '0px' || style.top === '0'
      const hasZIndex = parseInt(style.zIndex || '0') > 0

      return isFixed && isAtTop && hasZIndex
    })

    let maxHeight = 0

    fixedElements.forEach((el) => {
      const rect = el.getBoundingClientRect()
      const height = rect.height

      // 只考虑合理高度的元素（避免全屏遮罩等）
      if (rect.top <= 10 && height > 10 && height < 200) {
        maxHeight = Math.max(maxHeight, height)
      }
    })

    // 基础偏移量
    const baseOffset = 20

    // 限制最大偏移量，避免过大的值
    const maxAllowedOffset = 150
    let calculatedOffset = maxHeight > 0 ? Math.min(maxHeight + 20, maxAllowedOffset) : baseOffset

    // 如果计算的偏移量仍然很大，使用保守的默认值
    if (calculatedOffset > 100) {
      calculatedOffset = 80
    }

    return calculatedOffset
  } catch (error) {
    console.warn('计算动态偏移量时出错:', error)
    return 80 // 回退到默认值
  }
}

// 查找滚动容器
const findScrollContainer = (element: Element): Element | null => {
  let parent = element.parentElement

  while (parent) {
    const style = window.getComputedStyle(parent)
    const overflow = style.overflow + style.overflowY + style.overflowX

    if (/(auto|scroll)/.test(overflow)) {
      return parent
    }

    parent = parent.parentElement
  }

  return null
}

// 通过文本内容查找元素（备选定位方案）
const findElementByTextContent = (container: Element, description: string): HTMLElement | null => {
  try {
    // 根据描述确定要查找的文本模式
    let searchPatterns: RegExp[] = []

    if (description.includes('参考文献标题')) {
      searchPatterns = [/^参考文献\s*$/i, /^references\s*$/i, /^bibliography\s*$/i, /^文献\s*$/i]
    } else if (description.includes('参考文献条目')) {
      searchPatterns = [
        /^\s*\[?\d+\]?\s*[^\s]/, // [1] 或 1. 开头
        /^\s*\d+\.\s*/, // 数字加点
        /^\s*\[\d+\]\s*/ // 方括号数字
      ]
    }

    if (searchPatterns.length === 0) {
      return null
    }

    // 在容器中查找匹配的元素
    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {
      acceptNode: (node) => {
        const element = node as HTMLElement
        const tagName = element.tagName?.toLowerCase()

        // 只检查可能包含目标内容的元素
        if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div'].includes(tagName)) {
          return NodeFilter.FILTER_ACCEPT
        }
        return NodeFilter.FILTER_SKIP
      }
    })

    let currentNode: HTMLElement | null = null

    while ((currentNode = walker.nextNode() as HTMLElement)) {
      const textContent = currentNode.textContent?.trim() || ''

      for (const pattern of searchPatterns) {
        if (pattern.test(textContent)) {
          return currentNode
        }
      }
    }

    return null
  } catch (error) {
    console.error('文本内容匹配时出错:', error)
    return null
  }
}

// 找到最佳的滚动目标元素
const findBestScrollTarget = (element: HTMLElement, editorElement: Element): HTMLElement => {
  let scrollTarget = element

  // 向上查找，寻找段落、标题或其他合适的块级元素
  while (scrollTarget && scrollTarget !== editorElement) {
    const tagName = scrollTarget.tagName?.toLowerCase()

    // 优先选择标题元素
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      break
    }

    // 其次选择段落元素
    if (['p'].includes(tagName)) {
      break
    }

    // 最后选择 div 等容器元素
    if (
      ['div'].includes(tagName) &&
      scrollTarget.textContent &&
      scrollTarget.textContent.trim().length < 200
    ) {
      break
    }

    scrollTarget = scrollTarget.parentElement as HTMLElement
  }

  // 如果没找到合适的元素或回到了编辑器根元素，使用原始元素
  if (!scrollTarget || scrollTarget === editorElement) {
    scrollTarget = element
  }

  return scrollTarget
}

// 高亮元素（可选的视觉反馈）
const highlightElement = async (element: Element): Promise<void> => {
  try {
    // 添加高亮样式
    const originalTransition = (element as HTMLElement).style.transition
    const originalBackground = (element as HTMLElement).style.backgroundColor

    ;(element as HTMLElement).style.transition = 'background-color 0.3s ease'
    ;(element as HTMLElement).style.backgroundColor = '#fff3cd' // 淡黄色高亮

    // 等待一段时间后移除高亮
    setTimeout(() => {
      ;(element as HTMLElement).style.backgroundColor = originalBackground

      // 再等待一段时间后恢复原始 transition
      setTimeout(() => {
        ;(element as HTMLElement).style.transition = originalTransition
      }, 300)
    }, 1500) // 高亮持续 1.5 秒
  } catch (error) {
    console.warn('添加高亮效果时出错:', error)
  }
}
