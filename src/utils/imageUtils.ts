import type { Editor } from '@tiptap/core'

/**
 * 1. 根据图片URL获取相关SVG的数据
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @returns SVG数据或null
 */
export function getSvgDataByImageUrl(editor: Editor, imageUrl: string): any | null {
  let svgData = null

  editor.state.doc.descendants((node) => {
    if (node.type.name === 'image' && node.attrs.src === imageUrl) {
      svgData = node.attrs.svgData || null
      return false // 停止遍历
    }
  })

  return svgData
}

/**
 * 2. 根据图片URL判断是否有SVG数据
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @returns 是否有SVG数据
 */
export function hasSvgDataByImageUrl(editor: Editor, imageUrl: string): boolean {
  const svgData = getSvgDataByImageUrl(editor, imageUrl)
  return svgData !== null && svgData !== undefined
}

/**
 * 3. 保存SVG的JSON数据在对应image的属性中
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @param svgData SVG数据
 * @returns 是否保存成功
 */
export function saveSvgDataToImage(editor: Editor, imageUrl: string, svgData: any): boolean {
  try {
    const result = editor.commands.updateImageSvgData(imageUrl, svgData)
    return result
  } catch (error) {
    console.error('保存SVG数据失败:', error)
    return false
  }
}

/**
 * 创建带有SVG数据的图片节点
 * @param imageUrl 图片URL
 * @param svgData SVG数据
 * @param alt 图片alt属性
 * @param title 图片title属性
 * @param width 图片宽度
 * @param height 图片高度
 * @returns 图片节点对象
 */
export function createImageNodeWithSvgData(
  imageUrl: string,
  svgData: any,
  alt?: string,
  title?: string,
  width?: number,
  height?: number
) {
  return {
    type: 'image',
    attrs: {
      src: imageUrl,
      alt: alt || '示意图',
      title: title || '示意图',
      width,
      height,
      svgData
    }
  }
}

/**
 * 插入带有SVG数据的图片
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @param svgData SVG数据
 * @param alt 图片alt属性
 * @param title 图片title属性
 * @param width 图片宽度
 * @param height 图片高度
 * @returns 是否插入成功
 */
export function insertImageWithSvgData(
  editor: Editor,
  imageUrl: string,
  svgData: any,
  alt?: string,
  title?: string,
  width?: number,
  height?: number
): boolean {
  try {
    const result = editor.commands.setImageWithSvgData({
      src: imageUrl,
      alt: alt || '示意图',
      title: title || '示意图',
      svgData,
      ...(width && { width }),
      ...(height && { height })
    })
    return result
  } catch (error) {
    console.error('插入带SVG数据的图片失败:', error)
    return false
  }
}

/**
 * 更新现有图片的SVG数据
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @param newSvgData 新的SVG数据
 * @returns 是否更新成功
 */
export function updateImageSvgData(editor: Editor, imageUrl: string, newSvgData: any): boolean {
  return saveSvgDataToImage(editor, imageUrl, newSvgData)
}

/**
 * 删除图片的SVG数据
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @returns 是否删除成功
 */
export function removeSvgDataFromImage(editor: Editor, imageUrl: string): boolean {
  return saveSvgDataToImage(editor, imageUrl, null)
}

/**
 * 获取所有包含SVG数据的图片
 * @param editor TipTap编辑器实例
 * @returns 包含SVG数据的图片列表
 */
export function getAllImagesWithSvgData(editor: Editor): Array<{
  src: string
  svgData: any
  position: number
}> {
  const imagesWithSvgData: Array<{
    src: string
    svgData: any
    position: number
  }> = []

  editor.state.doc.descendants((node, pos) => {
    if (node.type.name === 'image' && node.attrs.svgData) {
      imagesWithSvgData.push({
        src: node.attrs.src,
        svgData: node.attrs.svgData,
        position: pos
      })
    }
  })

  return imagesWithSvgData
}

/**
 * 检查编辑器中是否存在指定URL的图片
 * @param editor TipTap编辑器实例
 * @param imageUrl 图片URL
 * @returns 是否存在该图片
 */
export function hasImageByUrl(editor: Editor, imageUrl: string): boolean {
  let found = false

  editor.state.doc.descendants((node) => {
    if (node.type.name === 'image' && node.attrs.src === imageUrl) {
      found = true
      return false // 停止遍历
    }
  })

  return found
}
