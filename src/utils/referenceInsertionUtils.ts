/**
 * 参考文献自动插入工具类
 * 用于在标注插入后自动添加对应的参考文献条目
 */

import { extractReferenceRegion, extractReferencesRobust } from './referenceUtils'

/**
 * 检测文档是否存在参考文献区域
 * @param {any} editor - TipTap 编辑器实例
 * @returns {object} 参考文献区域信息
 */
export const detectReferenceSection = (editor: any) => {
  if (!editor || !editor.state) {
    return { exists: false, position: -1, hasHeading: false }
  }

  try {
    const region = extractReferenceRegion(editor)

    if (region.found) {
      return {
        exists: true,
        position: region.headingPos !== -1 ? region.headingPos : region.from,
        hasHeading: region.headingPos !== -1,
        from: region.from,
        to: region.to
      }
    }

    return { exists: false, position: -1, hasHeading: false }
  } catch (error) {
    return { exists: false, position: -1, hasHeading: false }
  }
}

/**
 * 在文档末尾创建参考文献区域
 * @param {any} editor - TipTap 编辑器实例
 * @returns {boolean} 是否成功创建
 */
export const createReferenceSection = (editor: any): boolean => {
  if (!editor || !editor.state) {
    return false
  }

  try {
    // 获取文档末尾位置
    const docSize = editor.state.doc.content.size

    // 创建参考文献标题和空行
    const referenceContent = [
      { type: 'paragraph', content: [] }, // 空行分隔
      {
        type: 'heading',
        attrs: { level: 2 },
        content: [{ type: 'text', text: '参考文献' }]
      },
      { type: 'paragraph', content: [] } // 为后续参考文献条目预留空间
    ]

    // 在文档末尾插入参考文献区域
    const success = editor.commands.insertContentAt(docSize, referenceContent, {
      updateSelection: false,
      parseOptions: {
        preserveWhitespace: 'full'
      }
    })

    if (success) {
      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

/**
 * 获取现有参考文献条目及其编号
 * @param {any} editor - TipTap 编辑器实例
 * @returns {Array} 现有参考文献条目信息
 */
export const getExistingReferences = (
  editor: any
): Array<{
  number: number
  text: string
  position: number
}> => {
  if (!editor || !editor.state) {
    return []
  }

  try {
    const references: Array<{ number: number; text: string; position: number }> = []
    const region = extractReferenceRegion(editor)

    if (!region.found) {
      return []
    }

    // 遍历参考文献区域内的内容
    editor.state.doc.nodesBetween(region.from, region.to, (node: any, pos: number) => {
      if (node.type.name === 'paragraph') {
        const text = node.textContent || ''

        // 匹配参考文献条目格式 [数字]
        const match = text.match(/^\[(\d+)\]\s*(.+)/)
        if (match) {
          const number = parseInt(match[1], 10)
          if (!isNaN(number)) {
            references.push({
              number,
              text: text.trim(),
              position: pos
            })
          }
        }
      }
    })

    // 按编号排序
    return references.sort((a, b) => a.number - b.number)
  } catch (error) {
    return []
  }
}

/**
 * 获取现有参考文献条目及其编号（按位置排序）
 * @param {any} editor - TipTap 编辑器实例
 * @returns {Array} 现有参考文献条目信息（按位置排序）
 */
export const getExistingReferencesByPosition = (
  editor: any
): Array<{
  number: number
  text: string
  position: number
}> => {
  if (!editor || !editor.state) {
    return []
  }

  try {
    const references: Array<{ number: number; text: string; position: number }> = []
    const region = extractReferenceRegion(editor)

    if (!region.found) {
      return []
    }

    // 遍历参考文献区域内的内容
    editor.state.doc.nodesBetween(region.from, region.to, (node: any, pos: number) => {
      if (node.type.name === 'paragraph') {
        const text = node.textContent || ''

        // 匹配参考文献条目格式 [数字]
        const match = text.match(/^\[(\d+)\]\s*(.+)/)
        if (match) {
          const number = parseInt(match[1], 10)
          if (!isNaN(number)) {
            references.push({
              number,
              text: text.trim(),
              position: pos
            })
          }
        }
      }
    })

    // 按位置排序
    return references.sort((a, b) => a.position - b.position)
  } catch (error) {
    return []
  }
}

/**
 * 完整显示现有参考文献列表
 * @param {any} editor - TipTap 编辑器实例
 * @param {string} title - 显示标题
 */
export const displayCompleteReferenceList = (
  editor: any,
  title: string = '📋 参考文献列表'
): void => {
  if (!editor || !editor.state) {
    return
  }

  try {
    const references = getExistingReferencesByPosition(editor)
    console.log(`${title}: ${references.length} 个参考文献`)
  } catch (error) {
    // 静默处理错误
  }
}

/**
 * 格式化参考文献条目
 * @param {number} number - 参考文献编号
 * @param {string} content - 参考文献内容
 * @returns {string} 格式化后的参考文献条目
 */
// export const formatReferenceItem = (
//   number: number,
//   content: string = REFERENCE_TEMPLATE
// ): string => {
//   return `[${number}] ${content}`
// }

/**
 * 在指定位置插入参考文献条目
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} insertPosition - 插入位置
 * @param {number} referenceNumber - 参考文献编号
 * @param {string} content - 参考文献内容
 * @returns {boolean} 是否成功插入
 */
// export const insertReferenceItem = (
//   editor: any,
//   insertPosition: number,
//   referenceNumber: number,
//   content: string = REFERENCE_TEMPLATE
// ): boolean => {
//   if (!editor || !editor.state) {
//     return false
//   }

//   try {
//     const formattedContent = formatReferenceItem(referenceNumber, content)

//     const referenceNode = {
//       type: 'paragraph',
//       content: [{ type: 'text', text: formattedContent }]
//     }

//     const success = editor.commands.insertContentAt(insertPosition, referenceNode, {
//       updateSelection: false,
//       parseOptions: {
//         preserveWhitespace: 'full'
//       }
//     })

//     if (success) {
//       return true
//     } else {
//       return false
//     }
//   } catch (error) {
//     return false
//   }
// }

/**
 * 计算新参考文献条目的插入位置（基于目标编号在序列中的正确位置）
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} referenceNumber - 要插入的参考文献编号
 * @returns {number} 插入位置
 */
// export const calculateReferenceInsertPosition = (editor: any, referenceNumber: number): number => {
//   if (!editor || !editor.state) {
//     return -1
//   }

//   try {
//     // 使用按位置排序的参考文献列表
//     const existingReferencesByPosition = getExistingReferencesByPosition(editor)

//     const region = extractReferenceRegion(editor)

//     if (!region.found) {
//       return -1
//     }

//     if (existingReferencesByPosition.length === 0) {
//       // 没有现有参考文献，插入到参考文献区域的开始
//       return region.from
//     }

//     // 计算目标编号应该在序列中的位置（从1开始）
//     const targetPositionIndex = referenceNumber - 1

//     if (targetPositionIndex >= existingReferencesByPosition.length) {
//       const lastRef = existingReferencesByPosition[existingReferencesByPosition.length - 1]

//       // 计算最后一个参考文献条目的结束位置
//       let insertPosition = lastRef.position

//       // 遍历文档找到最后一个参考文献条目的实际结束位置
//       editor.state.doc.nodesBetween(region.from, region.to, (node: any, pos: number) => {
//         if (node.type.name === 'paragraph') {
//           const text = node.textContent || ''
//           const match = text.match(/^\[(\d+)\]\s*(.+)/)
//           if (match && parseInt(match[1], 10) === lastRef.number) {
//             insertPosition = pos + node.nodeSize
//           }
//         }
//       })

//       return insertPosition
//     } else {
//       // 插入到指定位置
//       const targetRef = existingReferencesByPosition[targetPositionIndex]
//       return targetRef.position
//     }
//   } catch (error) {
//     console.error('❌ 计算插入位置时发生错误:', error)
//     return -1
//   }
// }

/**
 * 重新编号现有参考文献条目
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} startNumber - 开始重新编号的编号
 * @returns {boolean} 是否成功重新编号
 */
// export const renumberExistingReferences = (editor: any, startNumber: number): boolean => {
//   if (!editor || !editor.state) {
//     return false
//   }

//   try {
//     const existingReferences = getExistingReferences(editor)
//     let updateCount = 0

//     // 更新需要重新编号的参考文献
//     existingReferences.forEach((ref) => {
//       if (ref.number >= startNumber) {
//         const newNumber = ref.number + 1
//         const newText = ref.text.replace(/^\[(\d+)\]/, `[${newNumber}]`)

//         // 更新文档中的参考文献条目
//         const nodeSize = editor.state.doc.nodeAt(ref.position)?.nodeSize || 1
//         editor.commands.deleteRange({ from: ref.position, to: ref.position + nodeSize })

//         const updatedNode = {
//           type: 'paragraph',
//           content: [{ type: 'text', text: newText }]
//         }

//         editor.commands.insertContentAt(ref.position, updatedNode, {
//           updateSelection: false,
//           parseOptions: {
//             preserveWhitespace: 'full'
//           }
//         })

//         updateCount++
//       }
//     })
//     return true
//   } catch (error) {
//     return false
//   }
// }

/**
 * 批量更新参考文献编号
 * @param {any} editor - TipTap 编辑器实例
 * @param {Array} numberMappings - 编号映射数组 [{oldNumber, newNumber}]
 * @returns {boolean} 是否成功更新
 */
// export const updateReferenceNumbers = (
//   editor: any,
//   numberMappings: Array<{ oldNumber: number; newNumber: number }>
// ): boolean => {
//   if (!editor || !editor.state || numberMappings.length === 0) {
//     return false
//   }

//   try {
//     const existingReferences = getExistingReferences(editor)
//     let updateCount = 0

//     // 创建编号映射表
//     const mappingMap = new Map(
//       numberMappings.map((mapping) => [mapping.oldNumber, mapping.newNumber])
//     )

//     // 按位置从后往前更新，避免位置偏移问题
//     const referencesToUpdate = existingReferences
//       .filter((ref) => mappingMap.has(ref.number))
//       .sort((a, b) => b.position - a.position)

//     referencesToUpdate.forEach((ref) => {
//       const newNumber = mappingMap.get(ref.number)
//       if (newNumber !== undefined) {
//         const newText = ref.text.replace(/^\[(\d+)\]/, `[${newNumber}]`)

//         // 更新文档中的参考文献条目
//         const nodeSize = editor.state.doc.nodeAt(ref.position)?.nodeSize || 1
//         editor.commands.deleteRange({ from: ref.position, to: ref.position + nodeSize })

//         const updatedNode = {
//           type: 'paragraph',
//           content: [{ type: 'text', text: newText }]
//         }

//         editor.commands.insertContentAt(ref.position, updatedNode, {
//           updateSelection: false,
//           parseOptions: {
//             preserveWhitespace: 'full'
//           }
//         })

//         updateCount++
//       }
//     })

//     return true
//   } catch (error) {
//     return false
//   }
// }

/**
 * 更新单个参考文献的编号
 * @param {any} editor - TipTap 编辑器实例
 * @param {object} reference - 参考文献对象
 * @param {number} newNumber - 新的编号
 * @returns {boolean} 是否成功更新
 */
const updateSingleReferenceNumber = (
  editor: any,
  reference: { number: number; text: string; position: number },
  newNumber: number
): boolean => {
  try {
    const newText = reference.text.replace(/^\[(\d+)\]/, `[${newNumber}]`)

    // 更新文档中的参考文献条目
    const nodeSize = editor.state.doc.nodeAt(reference.position)?.nodeSize || 1
    editor.commands.deleteRange({ from: reference.position, to: reference.position + nodeSize })

    const updatedNode = {
      type: 'paragraph',
      content: [{ type: 'text', text: newText }]
    }

    editor.commands.insertContentAt(reference.position, updatedNode, {
      updateSelection: false,
      parseOptions: {
        preserveWhitespace: 'full'
      }
    })

    return true
  } catch (error) {
    return false
  }
}

/**
 * 规范化参考文献编号，确保所有编号连续
 * @param {any} editor - TipTap 编辑器实例
 * @returns {boolean} 是否成功更新
 */
export const updateReferencesAfterInsertion = (editor: any): boolean => {
  if (!editor || !editor.state) {
    return false
  }

  try {
    let updateCount = 0

    // 获取当前所有参考文献（按位置排序，确保顺序正确）
    const currentReferences = getExistingReferences(editor)

    // 使用简化算法：确保所有参考文献编号连续
    // 从后往前更新，避免位置偏移问题
    for (let i = currentReferences.length - 1; i >= 0; i--) {
      const ref = currentReferences[i]
      const correctNumber = i + 1 // 基于位置的正确编号

      if (ref.number !== correctNumber) {
        // 编号不正确，需要更新
        const success = updateSingleReferenceNumber(editor, ref, correctNumber)
        if (success) {
          updateCount++
        }
      }
    }
    return true
  } catch (error) {
    return false
  }
}

/**
 * 主要的参考文献插入函数
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} annotationNumber - 标注编号
 * @param {string} content - 参考文献内容（可选，使用默认模板）
 * @returns {boolean} 是否成功插入
 */
export const insertReferenceForAnnotation = async (
  editor: any,
  annotationNumber: number,
  content: string
): Promise<boolean> => {
  // 参数验证
  if (!editor || !editor.state) {
    return false
  }

  if (typeof annotationNumber !== 'number' || annotationNumber < 1) {
    return false
  }

  if (!content || typeof content !== 'string' || content.trim() === '') {
    return false
  }

  try {
    // 1. 完整显示插入前的参考文献状态
    displayCompleteReferenceList(editor, '📋 插入前的参考文献状态')

    // 2. 检测参考文献区域
    let referenceSection = detectReferenceSection(editor)

    // 3. 如果不存在参考文献区域，创建一个
    if (!referenceSection.exists) {
      const created = createReferenceSection(editor)
      if (!created) {
        return false
      }

      // 重新检测参考文献区域
      await new Promise((resolve) => setTimeout(resolve, 100)) // 等待DOM更新
      referenceSection = detectReferenceSection(editor)

      if (!referenceSection.exists) {
        return false
      }
    }

    // 4. 使用 extractReferencesRobust 获取现有参考文献
    const extractResult = await extractReferencesRobust(editor)
    const existingReferences = extractResult.references || []

    // 5. 解析现有参考文献的编号
    const parsedReferences = existingReferences.map((ref, index) => {
      const match = ref.text.match(/^\[(\d+)\]\s*(.+)/)
      if (match) {
        return {
          number: parseInt(match[1], 10),
          content: match[2],
          originalText: ref.text,
          position: ref.position || -1,
          isNew: false
        }
      } else {
        // 如果没有编号，给它分配一个临时编号
        return {
          number: index + 1,
          content: ref.text,
          originalText: ref.text,
          position: ref.position || -1,
          isNew: false
        }
      }
    })

    // 6. 创建新的参考文献条目
    const newReference = {
      number: -1,
      content: content,
      originalText: content,
      position: -1,
      isNew: true
    }

    // 在指定位置插入新参考文献
    const insertIndex = Math.max(0, Math.min(annotationNumber - 1, parsedReferences.length))
    const referencesWithNewItem = [...parsedReferences]
    referencesWithNewItem.splice(insertIndex, 0, newReference)

    // 8. 重新编号确保连续性
    const reorderedReferences = referencesWithNewItem.map((ref, index) => ({
      ...ref,
      number: index + 1,
      formattedText: `[${index + 1}] ${ref.content}`
    }))

    // 9. 更新文档中的参考文献区域
    const success = await updateReferenceSection(editor, reorderedReferences)

    if (success) {
      // 10. 完整显示更新后的参考文献状态
      displayCompleteReferenceList(editor, '📋 更新后的参考文献状态')

      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

/**
 * 更新文档中的参考文献区域
 * @param {any} editor - TipTap 编辑器实例
 * @param {Array} reorderedReferences - 重新排序后的参考文献列表
 * @returns {Promise<boolean>} 是否成功更新
 */
const updateReferenceSection = async (
  editor: any,
  reorderedReferences: any[]
): Promise<boolean> => {
  try {
    // 1. 检测参考文献区域
    const referenceSection = detectReferenceSection(editor)
    if (!referenceSection.exists) {
      return false
    }

    // 2. 清除现有的参考文献内容（保留标题）
    const tr = editor.state.tr

    // 找到参考文献内容的开始位置（跳过标题）
    let contentStart = referenceSection.from || 0
    const contentEnd = referenceSection.to || editor.state.doc.content.size

    // 如果有标题，跳过标题段落
    if (
      referenceSection.hasHeading &&
      referenceSection.from !== undefined &&
      referenceSection.to !== undefined
    ) {
      editor.state.doc.nodesBetween(
        referenceSection.from,
        referenceSection.to,
        (node: any, pos: number) => {
          if (node.type.name === 'heading' && pos >= (referenceSection.from || 0)) {
            contentStart = pos + node.nodeSize
            return false // 找到标题后停止
          }
          return true
        }
      )
    }

    // 3. 删除现有内容
    if (contentEnd > contentStart) {
      tr.delete(contentStart, contentEnd)
    }

    // 4. 插入新的参考文献内容
    let insertPos = contentStart
    reorderedReferences.forEach((ref) => {
      // 创建段落节点
      const paragraphNode = editor.schema.nodes.paragraph.create(
        {},
        editor.schema.text(ref.formattedText)
      )

      tr.insert(insertPos, paragraphNode)
      insertPos += paragraphNode.nodeSize
    })

    // 5. 应用事务
    editor.view.dispatch(tr)

    return true
  } catch (error) {
    return false
  }
}
