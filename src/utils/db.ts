import { openDB } from 'idb'

// 判断浏览器是否支持 indexedDB
const isIndexedDBSupported = () => {
  return 'indexedDB' in window
}

// 打开（或创建）数据库
const openDatabase = async () => {
  if (!isIndexedDBSupported()) {
    console.warn('当前浏览器不支持 IndexedDB')
    return null // 如果不支持，直接返回 null 或者做其他处理
  }

  const db = await openDB('EditorDB', 1, {
    upgrade(db) {
      // 创建一个表 documents 存储文档和其版本
      if (!db.objectStoreNames.contains('documents')) {
        const store = db.createObjectStore('documents', { keyPath: 'docId' })
        store.createIndex('versions', 'versions')
      }
    }
  })
  return db
}

// 时间格式化函数，格式：2024/11/11 16:24:25
const formatTimestamp = (date: Date) => {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}

// 存储文档版本历史
export const saveDocumentVersion = async (docId: any, versionId: any, content: any) => {
  // 如果浏览器不支持 indexedDB，则不做任何操作
  if (!isIndexedDBSupported()) return

  const db = await openDatabase()
  if (!db) return // 如果打开数据库失败，则不做任何操作

  // 获取当前文档的记录，如果没有就创建新记录
  const doc = (await db.get('documents', docId)) || { docId, versions: [] }

  // 限制每个文档最多存储 20 个版本
  if (doc.versions.length >= 20) {
    doc.versions.pop() // 如果超过 20 个版本，删除最旧的一个版本
  }

  // 获取当前时间戳并格式化
  const timestamp = formatTimestamp(new Date())

  // 将新版本内容添加到 versions 数组的开头，包含版本ID、内容和时间戳
  doc.versions.unshift({ versionId, content, timestamp })

  // 更新文档数据
  await db.put('documents', doc)
}
// 获取某个文档的所有版本
export const getDocumentVersions = async (docId: any) => {
  // 如果浏览器不支持 indexedDB，则不做任何操作
  if (!isIndexedDBSupported()) return []

  const db = await openDatabase()
  if (!db) return [] // 如果打开数据库失败，返回空数组

  const doc = await db.get('documents', docId)

  // 打印获取的文档数据，进行调试
  console.log('Fetched document:', doc)

  // 确保文档的 versions 存在，即使为空也要返回一个空数组
  return doc ? doc.versions || [] : []
}

// 删除某个文档的所有版本
export const deleteDocument = async (docId: any) => {
  // 如果浏览器不支持 indexedDB，则不做任何操作
  if (!isIndexedDBSupported()) return

  const db = await openDatabase()
  if (!db) return // 如果打开数据库失败，则不做任何操作

  await db.delete('documents', docId)
}
