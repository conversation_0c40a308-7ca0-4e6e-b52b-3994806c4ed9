import type { Editor } from '@tiptap/vue-3'

/**
 * 检查编辑器是否有有效的光标位置或选中内容
 * @param editor Tiptap编辑器实例
 * @returns boolean 是否有有效的光标位置
 */
export const checkEditorFocus = (editor: Editor | null | undefined, type: string): boolean => {
  if (!editor) {
    return false
  }

  // console.log('from, to ==》', editor.isFocused, editor.view.hasFocus())
  if (type == 'table') {
    return editor.isFocused || editor.view.hasFocus()
  }
  const { from, to } = editor.state.selection
  // console.log('from, to ==》', from, to)
  const docSize = editor.state.doc.content.size
  // console.log('docSize ==》', docSize)
  if (docSize === 0) {
    return true
  }
  // if (from == 1 && to == 1) {
  //   // editor
  //   //   .chain()
  //   //   .focus() // 确保光标回到编辑器
  //   //   .insertContent(' ') // 插入一个空格
  //   //   .run()
  //   return false
  // }
  return from >= 0 && from <= docSize && to >= 0 && to <= docSize
}
