/**
 * 标注工具类
 * 用于管理文档中的标注编号和相关功能
 * 支持智能重排序和格式化标注
 */

// 全局标注计数器
let annotationCounter = 0

/**
 * 获取下一个标注编号（简单递增，用于向后兼容）
 * @returns {number} 下一个可用的标注编号
 */
export const getNextAnnotationNumber = (): number => {
  annotationCounter += 1
  return annotationCounter
}

/**
 * 获取文档中所有现有的标注编号
 * @param {any} editor - TipTap 编辑器实例
 * @returns {number[]} 所有现有标注编号的数组
 */
export const getAllAnnotationNumbers = (editor: any): number[] => {
  if (!editor || !editor.state) {
    return []
  }

  try {
    // 获取编辑器的 HTML 内容
    const htmlContent = editor.getHTML()

    // 使用正则表达式匹配所有上标标注
    const supRegex = /<sup>\[(\d+)\]<\/sup>/g
    const matches = [...htmlContent.matchAll(supRegex)]

    // 提取所有编号并去重排序
    const numbers = matches
      .map((match) => parseInt(match[1], 10))
      .filter((num) => !isNaN(num))
      .sort((a, b) => a - b)

    // 去重
    return [...new Set(numbers)]
  } catch (error) {
    console.error('获取标注编号时发生错误:', error)
    return []
  }
}

/**
 * 获取缺失的最小标注编号
 * @param {any} editor - TipTap 编辑器实例
 * @returns {number} 缺失的最小编号，如果没有缺失则返回最大编号+1
 */
export const getSmallestMissingAnnotationNumber = (editor: any): number => {
  if (!editor || !editor.state) {
    return 1
  }

  try {
    const existingNumbers = getAllAnnotationNumbers(editor)

    if (existingNumbers.length === 0) {
      return 1
    }

    // 找出缺失的最小数字
    for (let i = 1; i <= existingNumbers.length + 1; i++) {
      if (!existingNumbers.includes(i)) {
        return i
      }
    }

    // 如果没有缺失，返回最大编号+1
    return Math.max(...existingNumbers) + 1
  } catch (error) {
    console.error('计算缺失编号时发生错误:', error)
    return 1
  }
}

/**
 * 根据插入位置智能分配标注编号（保持向后兼容）
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} _insertPosition - 插入位置（已废弃，保持兼容性）
 * @returns {number} 应该分配的标注编号
 */
export const getInsertionAnnotationNumber = (editor: any, _insertPosition: number): number => {
  // 新的实现：直接返回缺失的最小编号，不考虑插入位置
  return getSmallestMissingAnnotationNumber(editor)
}

/**
 * 获取当前标注计数
 * @returns {number} 当前的标注计数
 */
export const getCurrentAnnotationCount = (): number => {
  return annotationCounter
}

/**
 * 重置标注编号计数器
 * @param {number} startNumber - 重置后的起始编号，默认为 0
 */
export const resetAnnotationCounter = (startNumber: number = 0): void => {
  annotationCounter = startNumber
}

/**
 * 设置标注计数器到指定值
 * @param {number} count - 要设置的计数值
 */
export const setAnnotationCounter = (count: number): void => {
  if (count < 0) {
    console.warn('标注计数器不能设置为负数，已设置为 0')
    annotationCounter = 0
  } else {
    annotationCounter = count
  }
}

/**
 * 获取所有标注及其在文档中的位置
 * @param {any} editor - TipTap 编辑器实例
 * @returns {Array} 标注信息数组，包含编号和位置
 */
export const getAllAnnotationsWithPositions = (
  editor: any
): Array<{
  number: number
  position: number
  element: Element
}> => {
  if (!editor || !editor.view) {
    return []
  }

  try {
    const annotations: Array<{ number: number; position: number; element: Element }> = []
    const doc = editor.view.dom

    // 查找所有上标元素
    const supElements = doc.querySelectorAll('sup')

    supElements.forEach((supElement: Element) => {
      const text = supElement.textContent || ''
      // 匹配 [数字] 格式
      const match = text.match(/^\[(\d+)\]$/)

      if (match) {
        const number = parseInt(match[1], 10)
        if (!isNaN(number)) {
          // 获取元素在文档中的位置
          const position = getElementPosition(editor, supElement)
          if (position !== -1) {
            annotations.push({
              number,
              position,
              element: supElement
            })
          }
        }
      }
    })

    // 按位置排序
    return annotations.sort((a, b) => a.position - b.position)
  } catch (error) {
    console.error('获取标注位置时发生错误:', error)
    return []
  }
}

/**
 * 获取DOM元素在编辑器中的位置
 * @param {any} editor - TipTap 编辑器实例
 * @param {Element} element - DOM元素
 * @returns {number} 位置索引，-1表示未找到
 */
const getElementPosition = (editor: any, element: Element): number => {
  try {
    const view = editor.view
    const pos = view.posAtDOM(element, 0)
    return pos
  } catch (error) {
    console.warn('无法获取元素位置:', error)
    return -1
  }
}

/**
 * 从编辑器内容中扫描现有标注并同步计数器
 * @param {any} editor - TipTap 编辑器实例
 * @returns {number} 找到的最大标注编号
 */
export const syncAnnotationCounterFromEditor = (editor: any): number => {
  if (!editor || !editor.state) {
    console.warn('无效的编辑器实例')
    return 0
  }

  try {
    // 获取编辑器的 HTML 内容
    const htmlContent = editor.getHTML()

    // 使用正则表达式匹配所有上标标注 - 更新为新格式
    const supRegex = /<sup>\[(\d+)\]<\/sup>/g
    const matches = [...htmlContent.matchAll(supRegex)]

    if (matches.length === 0) {
      resetAnnotationCounter(0)
      return 0
    }

    // 提取所有编号并找到最大值
    const numbers = matches.map((match) => parseInt(match[1], 10)).filter((num) => !isNaN(num))
    const maxNumber = Math.max(...numbers)

    // 将计数器设置为最大编号
    setAnnotationCounter(maxNumber)

    return maxNumber
  } catch (error) {
    console.error('同步标注计数器时发生错误:', error)
    return 0
  }
}

/**
 * 验证选中文本是否适合添加标注
 * @param {any} editor - TipTap 编辑器实例
 * @returns {object} 验证结果对象
 */
export const validateTextSelection = (
  editor: any
): {
  isValid: boolean
  message: string
  selection?: { from: number; to: number; text: string }
} => {
  if (!editor || !editor.state) {
    return {
      isValid: false,
      message: '编辑器未初始化'
    }
  }

  const { from, to } = editor.state.selection

  if (from === to) {
    return {
      isValid: false,
      message: '请先选择要添加标注的文本'
    }
  }

  const selectedText = editor.view.state.doc.textBetween(from, to)

  if (!selectedText || selectedText.trim().length === 0) {
    return {
      isValid: false,
      message: '选中的文本不能为空'
    }
  }

  if (selectedText.length > 200) {
    return {
      isValid: false,
      message: '选中的文本过长，建议选择较短的文本片段'
    }
  }

  return {
    isValid: true,
    message: '文本选择有效',
    selection: {
      from,
      to,
      text: selectedText
    }
  }
}

/**
 * 重新排序所有标注编号
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} insertedPosition - 新插入标注的位置
 * @param {number} insertedNumber - 新插入标注的编号
 * @returns {boolean} 是否成功重排序
 */
export const reorderAllAnnotations = (
  editor: any,
  insertedPosition: number,
  insertedNumber: number
): boolean => {
  if (!editor || !editor.view) {
    return false
  }

  try {
    // 获取所有现有标注
    const annotations = getAllAnnotationsWithPositions(editor)

    // 更新需要重新编号的标注
    let updateCount = 0
    annotations.forEach((annotation) => {
      if (annotation.position > insertedPosition && annotation.number >= insertedNumber) {
        // 需要将编号加1
        const newNumber = annotation.number + 1
        updateAnnotationNumber(annotation.element, newNumber)
        updateCount++
      }
    })

    return true
  } catch (error) {
    console.error('重排序标注时发生错误:', error)
    return false
  }
}

/**
 * 更新单个标注元素的编号
 * @param {Element} element - 标注元素
 * @param {number} newNumber - 新编号
 */
const updateAnnotationNumber = (element: Element, newNumber: number): void => {
  try {
    // 使用 requestAnimationFrame 确保 DOM 更新在下一帧执行
    requestAnimationFrame(() => {
      element.textContent = `[${newNumber}]`
    })
  } catch (error) {
    console.error('更新标注编号时发生错误:', error)
  }
}

/**
 * 格式化标注显示
 * @param {number} number - 标注编号
 * @param {string} format - 格式类型 ('number' | 'bracket' | 'parenthesis')
 * @returns {string} 格式化后的标注字符串
 */
export const formatAnnotation = (
  number: number,
  format: 'number' | 'bracket' | 'parenthesis' = 'bracket'
): string => {
  switch (format) {
    case 'bracket':
      return `[${number}]`
    case 'parenthesis':
      return `(${number})`
    case 'number':
      return `${number}`
    default:
      return `[${number}]` // 默认使用中括号格式
  }
}

/**
 * 重新排序整个文档的所有标注
 * @param {any} editor - TipTap 编辑器实例
 * @returns {boolean} 是否成功重排序
 */
export const reorderAllAnnotationsInDocument = (editor: any): boolean => {
  if (!editor || !editor.view) {
    return false
  }

  try {
    // 获取所有标注按位置排序
    const annotations = getAllAnnotationsWithPositions(editor)

    if (annotations.length === 0) {
      return true
    }

    // 重新分配连续编号
    annotations.forEach((annotation, index) => {
      const newNumber = index + 1
      if (annotation.number !== newNumber) {
        updateAnnotationNumber(annotation.element, newNumber)
      }
    })

    // 更新计数器
    setAnnotationCounter(annotations.length)

    return true
  } catch (error) {
    console.error('全文档重排序时发生错误:', error)
    return false
  }
}

/**
 * 获取指定位置前的所有上标注释
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} position - 指定位置
 * @returns {Array} 位置前的上标信息数组
 */
export const getAnnotationsBeforePosition = (
  editor: any,
  position: number
): Array<{
  number: number
  position: number
  element: Element
}> => {
  if (!editor || !editor.view) {
    return []
  }

  try {
    const allAnnotations = getAllAnnotationsWithPositions(editor)
    return allAnnotations.filter((annotation) => annotation.position < position)
  } catch (error) {
    console.error('获取位置前上标时发生错误:', error)
    return []
  }
}

/**
 * 获取指定位置后的所有上标注释
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} position - 指定位置
 * @returns {Array} 位置后的上标信息数组
 */
export const getAnnotationsAfterPosition = (
  editor: any,
  position: number
): Array<{
  number: number
  position: number
  element: Element
}> => {
  if (!editor || !editor.view) {
    return []
  }

  try {
    const allAnnotations = getAllAnnotationsWithPositions(editor)
    return allAnnotations.filter((annotation) => annotation.position > position)
  } catch (error) {
    console.error('获取位置后上标时发生错误:', error)
    return []
  }
}

/**
 * 批量更新指定位置后的上标编号
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} position - 指定位置
 * @param {number} minNumber - 最小需要更新的编号
 * @returns {Array} 更新的上标信息数组
 */
export const updateAnnotationsAfterPosition = (
  editor: any,
  position: number,
  minNumber: number
): Array<{
  oldNumber: number
  newNumber: number
  position: number
}> => {
  if (!editor || !editor.view) {
    return []
  }

  try {
    const annotationsAfter = getAnnotationsAfterPosition(editor, position)
    const updatedAnnotations: Array<{
      oldNumber: number
      newNumber: number
      position: number
    }> = []

    // 过滤出需要更新的上标（编号 >= minNumber）
    const annotationsToUpdate = annotationsAfter.filter(
      (annotation) => annotation.number >= minNumber
    )

    // 按位置排序，确保从后往前更新（避免位置偏移问题）
    annotationsToUpdate.sort((a, b) => b.position - a.position)

    annotationsToUpdate.forEach((annotation) => {
      const oldNumber = annotation.number
      const newNumber = oldNumber + 1

      // 更新DOM元素
      updateAnnotationNumber(annotation.element, newNumber)

      updatedAnnotations.push({
        oldNumber,
        newNumber,
        position: annotation.position
      })
    })

    return updatedAnnotations
  } catch (error) {
    return []
  }
}

/**
 * 检测字符是否为标点符号（排除括号类符号）
 * @param {string} char - 要检测的字符
 * @returns {boolean} 是否为标点符号
 */
export const isPunctuation = (char: string): boolean => {
  if (!char || char.length !== 1) {
    return false
  }

  // 中文标点符号（排除括号类）
  const chinesePunctuation = '。，；：？！、'

  // 英文标点符号（排除括号类）
  const englishPunctuation = '.,;:?!'

  // 检查是否为标点符号
  return chinesePunctuation.includes(char) || englishPunctuation.includes(char)
}

/**
 * 根据标点符号调整上标插入位置
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} position - 原始插入位置
 * @returns {number} 调整后的插入位置
 */
export const adjustInsertPositionForPunctuation = (editor: any, position: number): number => {
  if (!editor || !editor.state || position <= 0) {
    return position
  }

  try {
    // 获取插入位置前的字符
    const charBefore = editor.view.state.doc.textBetween(position - 1, position)

    // 如果前一个字符是标点符号，将插入位置前移一位
    if (isPunctuation(charBefore)) {
      console.log(
        `🔧 检测到标点符号 "${charBefore}"，将上标插入位置从 ${position} 调整为 ${position - 1}`
      )
      return position - 1
    }

    return position
  } catch (error) {
    console.error('调整插入位置时发生错误:', error)
    return position
  }
}

/**
 * 基于位置的智能编号策略
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} insertPosition - 插入位置
 * @returns {number} 应该分配的编号
 */
export const getPositionBasedAnnotationNumber = (editor: any, insertPosition: number): number => {
  if (!editor || !editor.view) {
    return 1
  }

  try {
    const annotationsBefore = getAnnotationsBeforePosition(editor, insertPosition)

    if (annotationsBefore.length === 0) {
      return 1
    }

    // 找到位置前的最大编号
    const maxNumberBefore = Math.max(...annotationsBefore.map((annotation) => annotation.number))
    return maxNumberBefore + 1
  } catch (error) {
    console.error('计算基于位置的编号时发生错误:', error)
    return 1
  }
}

/**
 * 获取标注统计信息
 * @param {any} editor - TipTap 编辑器实例
 * @returns {object} 标注统计信息
 */
export const getAnnotationStats = (
  editor: any
): {
  totalAnnotations: number
  maxNumber: number
  currentCounter: number
  annotationNumbers: number[]
} => {
  const maxNumber = syncAnnotationCounterFromEditor(editor)

  try {
    const htmlContent = editor.getHTML()
    const supRegex = /<sup>\[(\d+)\]<\/sup>/g
    const matches = [...htmlContent.matchAll(supRegex)]
    const numbers = matches.map((match) => parseInt(match[1], 10)).filter((num) => !isNaN(num))

    return {
      totalAnnotations: numbers.length,
      maxNumber,
      currentCounter: getCurrentAnnotationCount(),
      annotationNumbers: numbers.sort((a, b) => a - b)
    }
  } catch (error) {
    console.error('获取标注统计信息时发生错误:', error)
    return {
      totalAnnotations: 0,
      maxNumber: 0,
      currentCounter: getCurrentAnnotationCount(),
      annotationNumbers: []
    }
  }
}
