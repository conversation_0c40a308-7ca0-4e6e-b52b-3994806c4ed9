export interface SubmissionEditInfo {
  id: string
  createTime: Date
  userId: string
  submissionId: string
  title: string
  editorData: string
  status: number
  aiUsableCount: number
}

// "template": "",
//         // 替换
//         "replacements": {
//             "topic_cn": "",
//             "topic_en": ""
//         },

//         // 中文、英文摘要
//         "rich_abstract_cn": {},
//         "rich_abstract_en": {},
//         // 正文
//         "main_content": {},
//         // 以上三个是编辑器的json对象

//         // 纯文本, 短文创作时会有这个值
//         "text": ""

export interface Replacements {
  topic_cn: string
  topic_en: string
}
export interface EditorDataInfo {
  template: string
  replacements: Replacements
  rich_abstract_cn: object
  rich_abstract_en: object
  main_content: object
  text: string
}

export interface DoAiActionParams {
  submissionId: string
  code: string
  content: string
  teamId: string
  params: any
}

export interface SubmissionSaveParams {
  submissionId: string
  title: string
  editorData: string
}
export interface ExportCodeParams {
  id: string
  template?: string
}
