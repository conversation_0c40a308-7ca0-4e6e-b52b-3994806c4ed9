export interface LoginMobileRes {
  expiresTime: number
  isNeedMobileVerify: boolean
  isNewUser: boolean
  token: string
  userId: string
}
export interface LocalCacheUserInfo {
  token: string
}

export interface KnowledgeAssistantMemberInfo {
  userId: string
  vipLevel: number
  vipExpireTime: string
  usedWord: number
  maxWord: number
  maxChat: number
  usedChat: number
  freeAIOnlineEditing: number
  coinNum: number
  vipCode: string
  spaceQuotaBytes: number
  isVip: boolean
}

export interface AppLoginResultInfo {
  token: string
  userId: string // 返回有值
  nickname?: string // 返回为null
  avatar: string
  account?: string // 返回为null
  coinBalance?: number
  vipLevel?: number
  isSubscribe?: boolean
}

export interface AppUserInfo {
  nickname: string
  avatar: string
  id?: string
  userId: string
  account?: string
  coinBalance?: number
  vipLevel?: number
  isSubscribe?: boolean
  isAsked?: boolean
  authenticationStatus?: string
}

export interface ResponseUpdateUser {
  nickname: string
  avatar: string
}

export interface BindPhoneUserInfo {
  bindingUserInfo: AppUserInfo
  selfUserInfo: AppUserInfo
  bindingUserUseDetail: {
    coinBalance: number
    questionCount: number
    submissionCount: number
  }
  selfUseDetail: {
    coinBalance: number
    questionCount: number
    submissionCount: number
  }
}

export interface TeamSpaceInfo {
  id: string
  spaceQuotaBytes: number
  spaceType: string
  spaceUsedBytes: number
}

export interface TeamMemberInfo {
  id: string
  teamId: string
  userId: string
  name: string
  inviteTime: string
  joinTime: string
  mobile: string
  role: string
  status: string
}
export interface TeamInfo {
  id: string
  name: string
  logoUrl: string
  status: string
  description: string
  teamLevel: string
  maxMembers: number
  createTime?: string
  expiryDate?: string
  isCurrent?: boolean
  space: TeamSpaceInfo
  member: TeamMemberInfo
}
