export interface RepositoryFile {
  id: string
  createBy: string
  createTime: Date
  updateBy: null
  updateTime: null
  userId: string
  repositoryId: string
  fileType: string
  fileId: string
  fileName: string
  fileUrl: string
  wordCount: number
  processData: ProcessData
  editorData: string
}

export interface ProcessData {
  file_id: string
  inspiration_status: number
  inspiration_data: string
  parse_status: number
  summary_data: string
  summary_status: number
  word_count: number
}

export interface RepositoryFileGetDetailParams {
  id: string
}

export interface SaveEditorDataParams {
  id: string
  editorData: string
}
export interface GetExportCodeParams {
  id: string
}

export interface SearchScholar {
  abstract: string
  authors: string
  citations: number
  cite_url: string
  data_cid: string
  index: number
  main_url: string
  pdf_url: string
  publication: string
  title: string
  year: string
}
