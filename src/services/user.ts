import { AUTO_LOGIN_TOKEN, TOKENNAME, stroageKeyOfCurrentAutoLoginUser } from '@/config/config'
import { storage } from '@/utils/local-storage'
import type { AppLoginResultInfo } from './types/loginMobileRes'
import type { Response } from '@/services/types/response'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { getUserInfo, repositoryGetInfo } from '@/api/user'

const UserService = {
  isLogined: (): boolean => {
    if (storage.get(TOKENNAME)) {
      return true
    }
    return false
  },
  onLoginRes: async (res: Response<AppLoginResultInfo>, isLoadOpenRecord: boolean) => {
    console.log('onLoginRes ==>', res)
    //持久化
    storage.set(AUTO_LOGIN_TOKEN, res.data?.token)
    // 保存用户信息，注意此处暂时用户分享时获取头像和id，结构可能发生变化，注意使用
    storage.set(stroageKeyOfCurrentAutoLoginUser, JSON.stringify(res.data))

    storage.set(TOKENNAME, res.data?.token)

    console.log('登录成功')
    // if (StarloveUtil.isInTestServer()) {
    //   toast.success('登录成功')
    // }
    // await UserService.loadUserInfo()
  },
  setToken: async (_token: string) => {
    //持久化
    storage.set(AUTO_LOGIN_TOKEN, _token)
    storage.set(TOKENNAME, _token)
  },

  loadUserInfo: async () => {
    const store = useUserStore()

    const result = await getUserInfo()
    // console.log('getUserInfo result ==>', result)
    if (!result || !result.ok) {
      ElMessage.error(result.message || '用户信息加载错误')
      if (result.message == '用户已注销') {
        store.currentLoginInfo = undefined
        storage.remove(TOKENNAME)
        storage.remove('userInfo')
        storage.remove('userId')
        window.location.reload()
      }
      return
    }
    if (!result.data) {
      return
    }

    store.currentLoginInfo = result.data
    return result.data
  },
  onLoginByToken: async (token: string) => {
    //持久化
    storage.set(AUTO_LOGIN_TOKEN, token)
    storage.set(TOKENNAME, token)

    console.log('登录成功')
    // if (StarloveUtil.isInTestServer()) {
    //   toast.success('登录成功')
    // }
    // await UserService.loadUserInfo()
  },

  loadKnowledgeAssistantMemberInfo: async () => {
    const user = useUserStore()
    const result = await repositoryGetInfo({ teamId: user.getTeamId })
    if (!result || !result.ok) {
      return
    }
    if (!result.data) {
      return
    }
    user.setKnowledgeAssistantMemberInfo(result.data)
    return result.data
  },

  loadUserInfoAndAssistantMemberInfo: async () => {
    await UserService.loadUserInfo()
    await UserService.loadKnowledgeAssistantMemberInfo()
  }
}

/**
 * let dateTime = new Date();
console.log(dateTime); // Tue Sep 20 2022 16:08:58 GMT+0800 (中国标准时间)
let currentTime = format(dateTime, "yyyy-MM-dd HH:mm:ss");
console.log(currentTime); // 2022-09-20 16:09:33
 */
export { UserService }
