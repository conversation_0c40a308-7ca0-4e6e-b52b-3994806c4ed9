import type { Editor } from '@tiptap/core'
import { Extension } from '@tiptap/core'
import Superscript from '@tiptap/extension-superscript'
import CommandButton from '@/components/MenuCommands/CommandButton.vue'
import {
  resetAnnotationCounter,
  reorderAllAnnotationsInDocument,
  formatAnnotation,
  getPositionBasedAnnotationNumber,
  updateAnnotationsAfterPosition,
  adjustInsertPositionForPunctuation
} from '@/utils/annotationUtils'
import {
  insertReferenceForAnnotation,
  updateReferencesAfterInsertion
} from '@/utils/referenceInsertionUtils'

// 声明全局命令接口
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    annotationSuperscript: {
      /**
       * 在选中文本末尾插入带编号的上标
       */
      insertAnnotationSuperscript: () => ReturnType
      /**
       * 重置标注编号计数器
       */
      resetAnnotationNumbers: () => ReturnType
      /**
       * 重新排序整个文档的标注
       */
      reorderDocumentAnnotations: () => ReturnType
    }
  }
}

/**
 * 自定义标注上标扩展
 * 基于 TipTap Superscript 扩展，添加自动编号和智能插入功能
 */
const AnnotationSuperscript = Extension.create({
  name: 'annotationSuperscript',

  // 添加依赖的扩展
  addExtensions() {
    return [Superscript]
  },

  addOptions() {
    return {
      ...this.parent?.(),
      // 添加按钮配置
      button({ editor }: { editor: Editor; t: (...args: any[]) => string }) {
        return {
          component: CommandButton,
          componentProps: {
            command: () => {
              editor.commands.insertAnnotationSuperscript()
            },
            isActive: false, // 标注按钮不需要激活状态
            icon: 'superscript', // 使用上标图标
            tooltip: '插入标注'
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertAnnotationSuperscript:
        (options?: { referenceContent?: string }) =>
        ({ editor, commands, state }) => {
          try {
            // 检查是否有选中的文本
            const { from, to } = state.selection

            if (from === to) {
              return false
            }

            // 使用基于位置的智能编号策略
            const originalInsertPosition = to

            // 根据标点符号调整插入位置
            const adjustedInsertPosition = adjustInsertPositionForPunctuation(
              editor,
              originalInsertPosition
            )

            const annotationNumber = getPositionBasedAnnotationNumber(
              editor,
              adjustedInsertPosition
            )

            // 创建上标内容
            const formattedNumber = formatAnnotation(annotationNumber)
            const superscriptContent = `<sup>${formattedNumber}</sup>`

            // 将光标移动到调整后的插入位置
            commands.setTextSelection(adjustedInsertPosition)

            // 在调整后的位置插入上标
            const success = commands.insertContent(superscriptContent, {
              updateSelection: true,
              parseOptions: {
                preserveWhitespace: 'full'
              }
            })

            if (success) {
              // 使用 setTimeout 延迟执行后续操作，避免事务冲突
              setTimeout(() => {
                try {
                  // 1. 插入对应的参考文献（在正确的位置）
                  const referenceContent = options?.referenceContent || '请在此处添加参考文献内容'

                  insertReferenceForAnnotation(editor, annotationNumber, referenceContent)

                  // 2. 更新插入位置后的上标编号
                  const updatedAnnotations = updateAnnotationsAfterPosition(
                    editor,
                    adjustedInsertPosition,
                    annotationNumber
                  )

                  // 3. 如果有上标被更新，同步更新参考文献编号
                  if (updatedAnnotations.length > 0) {
                    updateReferencesAfterInsertion(editor)
                  }
                } catch (error) {
                  console.error('❌ 延迟执行标注更新时发生错误:', error)
                }
              }, 100) // 延迟100ms执行

              return true
            } else {
              console.error('❌ 插入标注失败')
              return false
            }
          } catch (error) {
            console.error('❌ 插入标注时发生错误:', error)
            return false
          }
        },

      resetAnnotationNumbers: () => () => {
        try {
          resetAnnotationCounter()
          console.log('标注编号计数器已重置')
          return true
        } catch (error) {
          console.error('重置标注编号时发生错误:', error)
          return false
        }
      },

      reorderDocumentAnnotations:
        () =>
        ({ editor }) => {
          try {
            const success = reorderAllAnnotationsInDocument(editor)

            return success
          } catch (error) {
            console.error('重排序文档标注时发生错误:', error)
            return false
          }
        }
    }
  },

  addKeyboardShortcuts() {
    return {
      // 添加快捷键 Ctrl+Shift+A (或 Cmd+Shift+A)
      'Mod-Shift-a': () => this.editor.commands.insertAnnotationSuperscript()
    }
  }
})

export default AnnotationSuperscript
