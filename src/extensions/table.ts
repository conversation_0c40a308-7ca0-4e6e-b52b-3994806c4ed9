import type { Editor } from '@tiptap/core'
import { Table as TiptapTable } from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import TablePopover from '@/components/MenuCommands/TablePopover/index.vue'

const Table = TiptapTable.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      button({ editor, t }: { editor: Editor; t: (...args: any[]) => string }) {
        return {
          component: TablePopover,
          componentProps: {
            editor
          }
        }
      }
    }
  }

  // 移除addExtensions方法，避免重复添加table相关扩展
  // 这些扩展应该在主编辑器配置中单独添加
  // addExtensions() {
  //   return [TableRow, TableHeader, TableCell];
  // },
})

export default Table
