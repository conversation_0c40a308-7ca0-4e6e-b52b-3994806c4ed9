import { getCurrentInstance, type ComponentInternalInstance } from 'vue'
// const { appContext } = getCurrentInstance() as ComponentInternalInstance;

// export default function useEventBus(): any {
//   return appContext.config.globalProperties.$mitt
// }

export default function useEventBus() {
  const internalInstance = getCurrentInstance()
  if (!internalInstance) {
    return
  }
  const emitter = internalInstance.appContext.config.globalProperties.emitter
  return emitter
}
