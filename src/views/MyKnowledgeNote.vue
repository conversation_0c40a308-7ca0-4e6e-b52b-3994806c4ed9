<template>
  <div class="my-knowledge-note" v-if="editor">
    <div class="my-knowledge-note-header">
      <div class="header-tools">
        <div>
          <span class="save-status">{{ isLoadingSave ? '保存中' : '已保存' }}</span>
          <el-icon
            @click="editor.chain().focus().undo().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-disabled': !editor.can().chain().focus().undo().run()
            }"
          >
            <Iconfont name="undo"></Iconfont>
          </el-icon>
          <el-icon
            class="toolbar-icon-button"
            @click="editor.chain().focus().redo().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-disabled': !editor.can().chain().focus().redo().run()
            }"
          >
            <Iconfont name="redo"></Iconfont>
          </el-icon>
          <el-divider direction="vertical" />
          <el-icon
            @click="editor.chain().focus().toggleBold().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('bold')
            }"
          >
            <Iconfont name="jiacu1"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleItalic().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('italic')
            }"
          >
            <Iconfont name="xieti"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleUnderline().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('underline')
            }"
          >
            <Iconfont name="xiahuaxian"></Iconfont>
          </el-icon>
          <el-icon
            @click="editor.chain().focus().toggleBulletList().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('underline')
            }"
          >
            <Iconfont name="unorderedlist"></Iconfont>
          </el-icon>
          <el-icon
            @click="editor.chain().focus().toggleOrderedList().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('orderedList')
            }"
          >
            <Iconfont name="orderedlist"></Iconfont>
          </el-icon>
        </div>

        <div>
          <el-button
            type="primary"
            class="export-word"
            @click="handlePressExportWord"
            :loading="isExprotWordLoading"
            >导出笔记</el-button
          >
        </div>
      </div>
    </div>
    <div class="my-knowledge-note-content">
      <editor-content :editor="editor" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import Iconfont from '@/components/Iconfont.vue'
import StarterKit from '@tiptap/starter-kit'
import { Placeholder } from '@tiptap/extension-placeholder'
import { useRoute } from 'vue-router'
import _ from 'lodash'
import { downloadFile, parentPostMessage, sleep } from '@/utils/utils'
import { ElMessage } from 'element-plus'
import {
  Api,
  getExportCode,
  repositoryFileGetDetail,
  repositorySaveEditorData
} from '@/api/repositoryFile'
import markdownit from 'markdown-it'

const updateDuration = 500
const editor = ref()
let id: any = ''
const isExprotWordLoading = ref(false)
const isLoadingSave = ref(false)

const handlePressExportWord = async () => {
  isExprotWordLoading.value = true
  await sleep(500)
  const res = await getExportCode({ id })
  isExprotWordLoading.value = false
  if (!res.success) {
    return
  }
  const code = res.data
  if (code) {
    downloadFile(`${import.meta.env.VITE_APP_API_BASE_URL || ''}${Api.exportWord}?code=${code}`, id)
  }
}
const saveData = async (id: string, editorData: any) => {
  isLoadingSave.value = true
  await repositorySaveEditorData({ id, editorData: JSON.stringify(editorData) })
  isLoadingSave.value = false
}

const onUpdate = ({ editor: _editor }: any) => {
  const content = _editor.getJSON()
  saveData(id, content)
  parentPostMessage('myKnowledgeNoteUpdate', {
    action: 'update',
    id,
    content
  })
}
const getKnowledgeAssistantDetailById = async (id: string) => {
  if (!id) {
    return ElMessage.error('id参数找不到')
  }
  const result = await repositoryFileGetDetail({ id })
  if (!result.ok || !result.data) {
    return
  }
  let editorData = ''
  try {
    if (result.data.editorData) {
      editorData = JSON.parse(result.data.editorData)
    }
    editor.value = new Editor({
      extensions: [
        Placeholder.configure({
          placeholder: '记录一下你的思考...'
        }),
        StarterKit
      ],
      content: editorData,
      onUpdate: _.debounce(onUpdate, updateDuration),
      autofocus: true
    })
    // editor.value.chain().focus()
  } catch (error) {
    ElMessage.error('数据转换失败')
    console.error(error)
  }
}
const setupData = async () => {
  try {
    const route = useRoute()
    id = route.query?.id
    const height = route.query?.height
    if (height) {
      const element = document.querySelector('body')
      if (element) {
        element.style.height = `${height}px`
      }
    }
    await getKnowledgeAssistantDetailById(id)
  } catch (error) {
    console.log(error)
  }
}
const isMarkdown = (text: string) => {
  // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()）等Markdown特征
  const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))|(\n(?=(\n+)))/gm
  return markdownRegex.test(text)
}
const handlePressPanelInsert = (textToInsert: string) => {
  textToInsert = textToInsert.replace(/\\n/g, '<br/>').replace(/\n/g, '<br/>')
  if (isMarkdown(textToInsert)) {
    const md = markdownit()
    textToInsert = md.render(textToInsert)
  }
  // 假设你想追加的文本是 "追加的文本"
  const textToAppend = textToInsert

  editor.value.commands.setContent(editor.value.getHTML() + textToAppend, false)
  setTimeout(() => {
    onUpdate({ editor: editor.value })
  }, 500)
}

function onMessage(e: any) {
  let { command, value } = e.data
  console.log(command, value, 'onMessage')
  switch (command) {
    case 'appendText': {
      if (!value) return
      handlePressPanelInsert(value.text)
      break
    }
  }
}
onMounted(() => {
  setupData()
  window.addEventListener('message', onMessage)
})
onBeforeUnmount(() => {
  // await saveDataNoParams()
  editor.value.destroy()
})
onUnmounted(() => {
  window.removeEventListener('message', onMessage)
})
</script>
<style lang="scss">
#app,
.ai-editor-theme {
  height: 100%;
}
.my-knowledge-note {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;

  &-header {
    padding: 0 30px;
    .header-tools {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 40px;
      box-shadow: 0 3px 15px -3px #0d142121;
    }
    .toolbar-icon-button {
      margin: 0px 5px;
      padding-left: 5px;
      padding-right: 5px;
      width: 29px;
      height: 29px;
      border-width: 0px;
      border-radius: 5px;

      &:hover {
        background-color: #f8f8f8;
      }
    }

    .toolbar-icon-button-is-active {
      color: rgb(21, 112, 239);
    }

    .toolbar-icon-button-disabled {
      color: #999999;
    }

    button {
      margin-left: 5px;
      margin-right: 5px;
      border-width: 0px;
      border-radius: 5px;
      background-color: transparent;
      white-space: nowrap;
      font-size: 14px;
      &:hover {
        background-color: #f8f8f8;
      }
    }
    .icon-unorderedlist,
    .icon-orderedlist {
      font-size: 16px;
    }
    .export-word {
      height: 27px;
      background: linear-gradient(to right, #3b82f6, #6366f1);
    }
    .save-status {
      padding: 0 10px;
      font-size: 14px;
      color: #999999;
      white-space: nowrap;
    }
  }
  &-content {
    overflow: auto;
    height: 100%;
    & > div {
      padding: 0 30px;
      height: calc(100% - 30px);
    }
  }
  .tiptap {
    height: 100%;
  }
  .PromiseMirror-focused {
    border-width: 0px;
    outline: -webkit-focus-ring-color auto 0px;
  }

  :focus-visible {
    outline: -webkit-focus-ring-color auto 0px;
  }
  p.is-editor-empty:first-child::before {
    color: #ccc;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
}
</style>
