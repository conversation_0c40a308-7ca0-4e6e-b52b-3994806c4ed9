.history-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: hidden;

  .header-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px 15px;

    .document-left {
      display: flex;
      justify-content: flex-start;
      /* 返回按钮靠左 */
      align-items: center;
      width: auto;
      /* 适应按钮内容宽度 */
    }

    .document-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex: 1;
      /* 中间部分占满剩余空间 */
      justify-content: space-between;
      /* 居中内容 */

      .document-left-title {
        margin-left: 20px;
        user-select: none;
      }

      .version-select-container {
        width: 40%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-evenly;

        &-time {
          user-select: none;
        }
      }

      @media (max-width: 768px) {
        .version-select-container {
          width: 100%;
          justify-content: center;
          /* 让版本选择器居中 */
        }
      }



    }

    .document-right {
      width: 30%;
    }

    @media (max-width: 768px) {

      .document-right {
        display: none;
      }

      .document-left {
        width: 10%;
      }

      .document-content {
        flex-direction: column;
        align-items: center;
        width: 90%;
        /* 在小屏幕上填满宽度 */
      }

      .document-left-title,
      .version-select-container-time {
        display: none;
        /* 隐藏标题和时间 */
        white-space: nowrap;
      }
    }
  }


  .recover {
    padding: 0 10px;

    /* 默认宽度设置 */
    .responsive-dialog {
      width: 400px;
      /* 默认宽度 */
    }

    /* 媒体查询，屏幕宽度小于 768px 时调整弹窗宽度 */
    @media (max-width: 768px) {
      .responsive-dialog {
        width: 80%;
        /* 或者设置成一个更小的像素值，比如 300px */
      }
    }
  }

  .page-header {
    // background-color: green;
    min-height: 86px;
    background-color: #f0f0f0;
    position: sticky;
    border-bottom: 1px solid #dddddd;
    padding: 18px;
    justify-content: center;
    align-items: center;
    display: flex;
    box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
    // box-shadow: 0 3px 15px -3px #f0f0f0;
    // box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
  }

  .page-content {
    padding: 15px 0;
    background-color: yellow;
    flex: 1; // 占据所有剩余空间
    overflow-y: auto; // 当内容过长时出现纵向滚动条
    background-color: #f0f0f0;
    display: flex;
    // max-width: 816px;
    justify-content: center;
    // align-items: center;
  }

  .free-times {
    height: 27px;
    background: #ecf6ff;
    border-radius: 5px;
    font-size: 13px;
    color: #3b82f6;
    line-height: 27px;
  }

  .export-word {
    height: 27px;
    background: linear-gradient(to right, #3b82f6, #6366f1);
  }

  ol li p {
    padding: 0;
    text-indent: 0;
  }

  .doc-directory {
    position: fixed;
    top: 100px;
    height: calc(100% - 100px);
    margin-left: auto;
    width: calc(50% - 415px);
    left: 0;

    ul {
      margin: 0;
      padding: 0;

      li {
        list-style: none;
      }
    }

    .doc-directory-title {
      color: #333;
      font-weight: bolder;
      padding: 6px 15px;
      font-size: 18px;
    }

    .doc-directory-list {
      height: 100%;
      padding-bottom: 55px;
      overflow-y: auto;
    }
  }

  @media screen and (max-width: 1116px) {
    .doc-directory {
      display: none;
    }
  }

  @media screen and (min-width: 1116px) {
    .doc-directory {
      padding-left: 15px;
    }
  }

  .toc-entity {
    padding: 6px 6px 6px 15px;
    margin: 5px 0;
    line-height: 1.8;
    border: none;
    cursor: pointer;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
  }

  .toc-header1 {
    color: #666;
    font-weight: 700;
  }

  .toc-header2 {
    margin-left: 15px;
    padding-left: 15px;
  }

  .toc-header3 {
    margin-left: 15px;
    padding-left: 30px;
  }
}

/* Basic editor styles */
.tiptap {
  >*+* {
    margin-top: 0.75em;
  }

  ul,
  ol {
    padding: 0 1rem;
  }

  blockquote {
    padding-left: 1rem;
    border-left: 2px solid rgba(#0d0d0d, 0.1);
  }
}

.bubble-menu-container {
  box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
  -webkit-box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);

  background-color: #ffffff;
  padding: 0.2rem;
  // border-radius: 0.5rem;
  border-radius: 0.1rem;

  border: 1px solid #eaeaea;
  // box-shadow: ;
  // min-width: 350px;
  // min-height: 50px;
  width: 540px;
}

.bubble-menu-container-free {
  width: 662px;
}

.bubble-menu {
  display: flex;
  // background-color: #0D0D0D;

  padding: 10px;

  // button {
  //   border: none;
  //   background: none;
  //   // color: #FFF;
  //   color: #333333;
  //   font-size: 0.85rem;
  //   font-weight: 500;
  //   padding: 0 0.2rem;
  //   opacity: 0.6;

  //   &:hover,
  //   &.is-active {
  //     opacity: 1;
  //   }
  // }

  // button.is-checked {
  //   // background-color: greenyellow;
  // }
}

.floating-menu {
  display: flex;
  background-color: #0d0d0d10;
  padding: 0.2rem;
  border-radius: 0.5rem;

  button {
    border: none;
    background: none;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0 0.2rem;
    opacity: 0.6;

    &:hover,
    &.is-active {
      opacity: 1;
    }
  }
}

.bubble-total {
  display: flex;
  flex-direction: column;
  height: 100%;

  .bubble-originalText {
    max-height: 50%;
    margin-bottom: 15%;
    overflow: auto;

    .bubble-originalText-text {
      line-height: 28px;
      color: #888888;
    }

    .bubble-originalText-span {
      cursor: pointer;
      color: #000000;
      font-size: 20px;
      font-weight: 500;

      &:hover {
        background-color: #f8f8f8;
      }
    }
  }

  .bubble-panel {
    max-height: 50%;
    // background-color: pink;
    // padding: 10px;
    // border-top: 1px solid #eaeaea;
  }
}

// .page-header {
//   background-color: #f9f9f9;
//   padding: 10px;
//   border: 1px solid #eaeaea;

//   button {
//     border: none;
//     background: none;
//     font-size: 0.85rem;
//     font-weight: 500;
//     padding: 0 0.2rem;
//     opacity: 0.6;

//     &:hover,
//     &.is-active {
//       opacity: 1;
//     }
//   }
// }

.xiaoin-title {
  margin: 15px 0;
  font-family: STSongti-SC, STSongti-SC;
  font-weight: 900;
  font-size: 30px;
  color: #000000;
  line-height: 53px;
}

.xiaoin-editor-frame {
  padding: 50px 100px;
  -webkit-box-flex: 0;
  -ms-flex: 0 1 816px;
  flex: 0 1 816px;
  min-width: 396px;
  background-color: #fff;
  margin: 0 auto auto;
  // line-height: 150%;
  // flex: 0 1 816px;
  // max-width: 816px;
  // width: 816px;
  // min-height: 1200px;
  // background-color: pink;

  // background-color: #ffffff;

  // box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
  // -webkit-box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
  // margin-top: 20px;
  // margin-bottom: 20px;
}

@media (max-width: 768px) {
  .xiaoin-editor-frame {
    padding: 50px 20px;
  }
}


.xiaoin-editor-content {
  font-family: STSongti-SC, 'Heiti SC Medium', 'Heiti SC Light', SimSun;
  font-weight: 400;
  font-size: 18px;
  color: #000000;

  h1 {
    // margin-top: 30px;
    // margin-bottom: 50px;
    font-size: 22px;
    line-height: 37px;
  }

  h2 {
    // margin-top: 30px;
    // margin-bottom: 30px;
    font-size: 20px;
    line-height: 33px;
  }

  h3 {
    font-size: 19px;
    line-height: 31px;
  }

  p {
    margin: 0;
    margin: 15px 0;
    text-indent: 2em;
    font-size: 16px;
    line-height: 31px;
  }

  p.reference-item {
    text-indent: 0;
  }

  p.no-indent {
    text-indent: 0;
    padding: 0;
    margin: 0;
    font-size: 16px;
    line-height: 31px;

    &:first-child {
      padding-top: 15px;
    }
  }

  // .no-indent {
  //   &:first-child {
  //     margin-top: 15px;
  //     padding-top: 15px;
  //   }
  // }
  .abstracts-title {
    margin-top: 30px;
    margin-bottom: 0;
  }

  .main-content {
    h1 {
      margin-top: 30px;
      margin-bottom: 0;
    }

    h2,
    h3 {
      margin-top: 15px;
      margin-bottom: 0;
    }
  }

  h1+.no-indent {
    margin-top: 15px;
  }

  img {
    max-width: 100%;
  }

  .image-uploading {
    max-width: 100%;
    padding: 0;
    margin: 0;
    margin-left: -38px;
  }
}

.PromiseMirror-focused {
  border-width: 0px;
  outline: -webkit-focus-ring-color auto 0px;
}

:focus-visible {
  outline: -webkit-focus-ring-color auto 0px;
}

.header-tools,
.bubble-tools {
  .toolbar-icon-button {
    margin: 0px 5px;
    padding-left: 5px;
    padding-right: 5px;
    width: 29px;
    height: 29px;
    // background-color: #f0f0f0;
    border-width: 0px;
    border-radius: 5px;

    &:hover {
      background-color: #f8f8f8;
    }
  }

  .toolbar-icon-button-is-active {
    color: rgb(21, 112, 239);
  }

  .toolbar-icon-button-disabled {
    color: #999999;
  }

  button {
    // width: 30px;
    // height: 30px;
    margin-left: 5px;
    margin-right: 5px;
    border-width: 0px;
    border-radius: 5px;
    background-color: transparent;
    white-space: nowrap;
    font-size: 14px;

    &:hover {
      background-color: #f8f8f8;
    }
  }

  .header-doc-title {
    padding: 0 5px;
    max-width: 210px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    white-space: nowrap;
  }

  .save-status {
    padding: 0 10px;
    font-size: 14px;
    color: #999999;
    white-space: nowrap;
  }
}

.header-tools {
  display: flex;
  align-items: center;
}

// .toolbar-icon-button{
//   background-color: pink;
// }

.highlight {
  background-color: yellow;
  // transition: background-color 2s;

  animation: fadeOut 3s forwards;
}

@keyframes fadeOut {
  from {
    background-color: yellow;
  }

  to {
    background-color: transparent;
  }
}

.toolbar-ddl {
  padding: 0 5px;
  // background-color: pink;
  display: inline-block;
  text-align: center;
  // padding-left: 5px;
  // padding-top: 20px;
  height: 15px;
  // display:  inline-flex;
  // align-items: center;
  // justify-content: center;
}

/* Table-specific styling */
.tiptap {
  table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    text-indent: 0;

    p {
      text-indent: 0;
      padding: 0;
    }

    td,
    th {
      min-width: 1em;
      border: 2px solid #ced4da;
      padding: 3px 5px;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;

      >* {
        margin-bottom: 0;
      }

      text-indent: 0;
    }

    th {
      font-weight: bold;
      text-align: left;
      background-color: #f1f3f5;
      text-indent: 0;
    }

    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: '';
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(200, 200, 255, 0.4);
      pointer-events: none;
    }

    .column-resize-handle {
      position: absolute;
      right: -2px;
      top: 0;
      bottom: -2px;
      width: 4px;
      background-color: #adf;
      pointer-events: none;
    }

    p {
      margin: 0;
    }
  }
}

/* Basic editor styles */
.ProseMirror {
  >*+* {
    margin-top: 0.75em;
  }

  .Tiptap-mathematics-editor {
    background: #202020;
    color: #fff;
    font-family: monospace;
    padding: 0.2rem 0.5rem;
  }

  .Tiptap-mathematics-render {
    cursor: pointer;
    padding: 0 0.25rem;
    transition: background 0.2s;

    &:hover {
      background: #eee;
    }
  }

  .Tiptap-mathematics-editor,
  .Tiptap-mathematics-render {
    border-radius: 0.25rem;
    display: inline-block;
    text-indent: 0;
  }
}

.file-break::after {
  content: '分页符';
  // display: block;
  // color: white;
  // background-color: gray;
  // margin-bottom: 10px;
  // text-align: center;
  display: block;
  // display: flex;
  color: #5d6a91;
  background-color: #dde3ec;
  margin-bottom: 10px;
  text-align: center;
  justify-content: center;
  border: 1px solid #9cb0c9;
  border-radius: 3px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.toc {
  text-align: center;
}

.toc::after {
  content: '目录';
  display: block;
  // display: flex;
  color: #5d6a91;
  background-color: #dde3ec;
  margin-bottom: 10px;
  text-align: center;
  justify-content: center;
  border: 1px solid #9cb0c9;
  border-radius: 3px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.document-title-input {
  background-color: pink;
  border: none;
  outline: none;
  box-shadow: 0 0 0 0;

  .is-focus {
    border: none;
    outline: none;
    box-shadow: 0 0 0 0;
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 0;
    outline: none;
  }

  .el-input {
    border: none;
    box-shadow: 0 0 0 0;
    outline: none;
  }

  input {
    border: none;
    outline: none;
    box-shadow: 0 0 0 0;
  }
}

.no-border-input {

  input,
  textarea {
    // font-size: 28px;
    outline: none;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    // font-size: 22.5pt;
    // font-weight: 700;
    // color: #1f1f1f;
    // height: 50px;
    padding: 0;
    text-align: center;

    font-family: STSongti-SC, STSongti-SC;
    font-weight: 900;
    font-size: 30px;
    color: #000000;
    font-size: 26px;
    line-height: 45px;
    height: 45px;
    word-break: break-all;
  }
}

.no-border-input .el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
  padding-left: 3px;
  padding-right: 3px;
}

.no-border-input .el-input__wrapper:focus {
  border-color: transparent !important;
  box-shadow: none !important;
}

.el-drawer__header {
  padding: 0 20px;
  margin: 0;
}

.count-words {
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}

.count-words-desc {
  padding-top: 10px;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
}

.red {
  color: #ff4242;
}

.blue {
  color: #3b82f6;
}

.btn-normal {
  width: 68px;
  height: 29px;
  line-height: 29px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #3b82f6;
  font-size: 14px;
  color: #3b82f6;
}

.to-recharge {
  background: linear-gradient(to right, #3b82f6, #6366f1);
  border: none;
  color: #fff;

  :hover {
    opacity: 0.85;
  }
}

.btn-cancel {
  margin-left: 30px !important;
}