<template>
  <div v-if="editor" class="history-page-container">
    <el-affix :offset="0">
      <!-- 版本选择下拉框 -->
      <div class="header-container">
        <!-- 返回按钮单独放置，确保始终在最左边 -->
        <div class="document-left">
          <el-button @click="goBack" type="plain" link>
            <el-icon class="document-left-icon"><ArrowLeftBold /></el-icon>
          </el-button>
        </div>

        <!-- 其他部分保持原有结构 -->
        <div class="document-content">
          <div class="document-left-title">{{ documentTitle || '未定义标题' }}</div>

          <div class="version-select-container">
            <el-select
              style="width: 140px"
              v-model="selectedVersion"
              placeholder="请选择版本"
              @change="handleVersionChange(selectedVersion)"
            >
              <el-option
                v-for="(version, index) in versions"
                :key="index"
                :label="'版本' + version.versionId"
                :value="version.versionId"
              />
            </el-select>

            <div class="version-select-container-time">{{ documentTime }}</div>

            <div class="recover">
              <el-button @click="dialogTableVisible = true" type="plain" link>恢复此版本</el-button>
              <el-dialog
                align-center
                v-model="dialogTableVisible"
                title="恢复"
                class="responsive-dialog"
                width="400"
              >
                <span>确认恢复此版本吗？</span>
                <template #footer>
                  <div class="dialog-footer">
                    <el-button @click="dialogTableVisible = false">取消</el-button>
                    <el-button type="primary" @click="fallback"> 确定 </el-button>
                  </div>
                </template>
              </el-dialog>
            </div>
          </div>
        </div>
        <div class="document-right"></div>
      </div>
    </el-affix>

    <div class="page-content" v-if="editor">
      <div class="xiaoin-editor-frame">
        <div class="xiaoin-editor-content">
          <div
            :class="
              editorData?.rich_abstract_cn &&
              Array.isArray(editorData?.rich_abstract_cn?.content) &&
              editorData?.rich_abstract_cn.content.length > 0
                ? 'abstracts'
                : 'hide'
            "
          >
            <h1 class="abstracts-title" id="abstracts-cn">摘要</h1>
            <editor-content :editor="editor0" />
          </div>
          <div
            :class="
              editorData?.rich_abstract_en &&
              Array.isArray(editorData?.rich_abstract_en?.content) &&
              editorData?.rich_abstract_en.content.length > 0
                ? 'abstracts'
                : 'hide'
            "
          >
            <h1 class="abstracts-title" id="abstracts-en">Abstract</h1>
            <editor-content :editor="editor1" />
          </div>

          <editor-content class="main-content" :editor="editor2" />
        </div>
      </div>
      <div class="doc-directory" :class="`${headerList.length > 0 ? '' : 'hide'}`">
        <div class="doc-directory-title">大纲</div>
        <ul class="doc-directory-list">
          <li
            v-for="item in headerList"
            :key="item.id"
            :class="`toc-entity toc-header${item.level} hasChildren`"
            @click="scrollToHeader(item.id)"
          >
            {{ item.text }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { EditorContent } from '@tiptap/vue-3'
import { ref, onMounted, onUnmounted, onBeforeUnmount, reactive } from 'vue'

import { doAiAction } from '@/api/submissionEdit'
import { createTipTapEditor } from '@/utils/utils'
import { ACTION_ERROR_CODE } from '@/utils/constants'
import markdownit from 'markdown-it'
import _ from 'lodash'
import 'katex/dist/katex.min.css'

import { save } from '@/api/submissionEdit'

import { getDocumentVersions } from '@/utils/db'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'

import { ArrowLeftBold } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const store = useUserStore()
const editor = ref(null)
const editor0 = ref(null)
const editor1 = ref(null)
const editor2 = ref(null)
const documentTitle = ref('')
const documentTime = ref('')

const dialogTableVisible = ref(false)

const editorData = ref(null)

const rechargeVisible = ref(false)
const isCanEditor = ref(false)

// 存储版本信息的响应式变量
const versions = ref([])

const selectedVersion = ref('') // 用来绑定下拉框的选择值

const activeDoAction = reactive({
  visible: false,
  code: '',
  codeText: '',
  originalText: '',
  activeAIText: '',
  isAIChanging: false,
  isLoading: false,
  countWords: 0,
  errorCode: ACTION_ERROR_CODE.NORMAL // 0正常， 1字数异常 2其他
})
let submissionId = ''
const updateDuration = 500
const onFocus = ({ editor: _editor }) => {
  editor.value = _editor
  isCanEditor.value = true
}
const updatebyData = ({ editor: _editor, field }) => {
  console.log(_editor, 'updatebyData')

  setTimeout(() => {
    let params = {}
    if (field === 'text') {
      params = {
        text: '',
        main_content: _editor.getJSON()
      }
    } else if (field === 'main_content') {
      params = {
        main_content: _editor.getJSON()
      }
    } else if (field === 'rich_abstract_cn') {
      params[field] = _editor.getJSON()
    } else if (field === 'rich_abstract_en') {
      params[field] = _editor.getJSON()
    }
  }, 300)
}
const onUpdate = ({ editor: _editor }) => {
  if (editorData.value.text) {
    updatebyData({ editor: _editor, field: 'text' })
  } else if (editorData.value.main_content) {
    updatebyData({ editor: _editor, field: 'main_content' })
  }
}
const onUpdateRichAbstractCn = ({ editor: _editor }) => {
  updatebyData({ editor: _editor, field: 'rich_abstract_cn' })
}
const onUpdateRichAbstractEn = ({ editor: _editor }) => {
  updatebyData({ editor: _editor, field: 'rich_abstract_en' })
}
const isMarkdown = (text) => {
  // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()）等Markdown特征
  const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))|(\n(?=(\n+)))/gm
  return markdownRegex.test(text)
}
let submissionEditInfo = ref(null)

// 文档 ID（假设是固定的）

const route = useRoute()

const docId = route.query?.id

// 加载文档所有版本

const loadVersions = async () => {
  try {
    // 加载所有版本
    const loadedVersions = await getDocumentVersions(docId)
    versions.value = loadedVersions

    // 如果有版本，默认选择最后一个版本
    if (versions.value.length > 0) {
      selectedVersion.value = versions.value[0].versionId // 选择最后一个版本
    }
  } catch (error) {
    console.error('加载版本失败:', error)
  }
}

const versionNumber = ref(null)

// 处理版本选择变化
const handleVersionChange = async (item) => {
  const versionId = item

  const index = versions.value.findIndex((item) => item.versionId === versionId)

  versionNumber.value = index

  // 加载完版本后，执行数据初始化
  await setupData()
}

// 数据初始化方法，加载并解析版本内容
const setupData = async () => {
  try {
    // 获取当前选择的版本并解析其内容
    const savedHistory = JSON.parse(versions.value[versionNumber.value || 0].content)

    documentTime.value = versions.value[versionNumber.value || 0].timestamp

    submissionEditInfo.value = savedHistory

    if (savedHistory.editorData) {
      const _editorData = savedHistory.editorData
      try {
        const editorDataObj = JSON.parse(_editorData)

        documentTitle.value = editorDataObj?.replacements?.topic_cn || savedHistory.title || ''

        editorData.value = editorDataObj
        console.log(editorDataObj, 'editorDataObj')

        // text有可能是markdown
        let _text = ''
        if (editorDataObj.text) {
          _text = editorDataObj.text
          if (isMarkdown(_text)) {
            const md = markdownit()
            _text = md.render(_text) // 转换为 HTML
          }
        } else if (editorDataObj.main_content) {
          _text = editorDataObj.main_content
        }

        // 重新创建 TipTap 编辑器
        if (editor2.value) {
          editor2.value.destroy() // 销毁旧编辑器
        }

        // 创建 TipTap 编辑器
        editor2.value = createTipTapEditor({
          defaultContent: _text,
          onFocus: ({ editor: _editor }) => {
            onFocus({ editor: _editor, field: 'main_content' })
          },
          onUpdate: _.debounce(onUpdate, updateDuration),
          readonly: true // Set this to false to allow editing
        })

        editor.value = editor2.value

        setHeaderList(editor2.value.getJSON())

        // 初始化摘要内容
        // 重新创建 TipTap 编辑器
        if (editor0.value) {
          editor0.value.destroy() // 销毁旧编辑器
        }

        if (
          editorDataObj?.rich_abstract_cn &&
          Array.isArray(editorDataObj?.rich_abstract_cn?.content) &&
          editorDataObj?.rich_abstract_cn.content.length > 0
        ) {
          editor0.value = createTipTapEditor({
            defaultContent: editorDataObj.rich_abstract_cn,
            onFocus: ({ editor: _editor }) => {
              onFocus({ editor: _editor, field: 'rich_abstract_cn' })
            },
            onUpdate: _.debounce(onUpdateRichAbstractCn, updateDuration),
            readonly: true // Set this to false to allow editing
          })
        }

        // 重新创建 TipTap 编辑器
        if (editor1.value) {
          editor1.value.destroy() // 销毁旧编辑器
        }

        if (
          editorDataObj?.rich_abstract_en &&
          Array.isArray(editorDataObj?.rich_abstract_en?.content) &&
          editorDataObj?.rich_abstract_en.content.length > 0
        ) {
          editor1.value = createTipTapEditor({
            defaultContent: editorDataObj.rich_abstract_en,
            onFocus: ({ editor: _editor }) => {
              onFocus({ editor: _editor, field: 'rich_abstract_en' })
            },
            onUpdate: _.debounce(onUpdateRichAbstractEn, updateDuration),
            readonly: true // Set this to false to allow editing
          })
        }
      } catch (error) {
        console.error('编辑器数据解析失败:', error)
      }
    }
  } catch (error) {
    console.error('加载版本内容失败:', error)
  }
}

const router = useRouter()

const fallback = async () => {
  const saveParams = {
    submissionId: route.query?.id,
    title: submissionEditInfo.value.title,
    editorData: submissionEditInfo.value.editorData
  }

  const res = await save(saveParams)

  if (!res.ok) {
    return
  }

  goBack()
}

const goBack = () => {
  router.back() // 或者 router.go(-1) 也可以
}

function onMessage(e) {
  let { command, value } = e.data

  switch (command) {
    case 'closeIframe': {
      if (!value) return
      rechargeVisible.value = false
      handleOKAIAction(activeDoAction.code)
      break
    }
  }
}
onMounted(async () => {
  // 加载版本信息
  await loadVersions()
  // 加载完版本后，执行数据初始化
  await setupData()
  window.addEventListener('message', onMessage)
})

onBeforeUnmount(() => {
  editor.value.destroy()
})
onUnmounted(() => {
  window.removeEventListener('message', onMessage)
})

// -------------------------弹出层选中文字---------------------
const textarea = ref('')
// -------------------------弹出层选中文字---------------------

const handleOKAIAction = async (code) => {
  activeDoAction.visible = true
  activeDoAction.isLoading = true
  activeDoAction.errorCode = ACTION_ERROR_CODE.NORMAL
  const res = await doAiAction({
    teamId: store.getTeamId,
    submissionId: submissionId,
    code,
    content: activeDoAction.originalText,
    params: {
      ask: textarea.value
    }
  })
  if (!res.success) {
    activeDoAction.isLoading = false
    return
  }
  activeDoAction.activeAIText = res.data
  textarea.value = '' // 辅助信息框恢复默认
  activeDoAction.isLoading = false
  if (submissionEditInfo.value.aiUsableCount > 0) {
    submissionEditInfo.value.aiUsableCount--
  }
}

const headerList = ref([])
const setHeaderList = (json) => {
  const headers = []
  if (
    editorData.value?.rich_abstract_cn &&
    Array.isArray(editorData.value?.rich_abstract_cn?.content) &&
    editorData.value?.rich_abstract_cn.content.length > 0
  ) {
    headers.push({
      text: '摘要',
      level: 1,
      id: 'abstracts-cn'
    })
  }
  if (
    editorData.value?.rich_abstract_en &&
    Array.isArray(editorData.value?.rich_abstract_en?.content) &&
    editorData.value?.rich_abstract_en.content.length > 0
  ) {
    headers.push({
      text: 'Abstract',
      level: 1,
      id: 'abstracts-en'
    })
  }
  let textList = []
  if (json?.content && Array.isArray(json?.content) && json.content.length > 0) {
    json.content.forEach((item) => {
      const { type, attrs, content } = item
      if (type === 'heading' && content && content.length > 0) {
        textList = []
        content.forEach((d) => {
          if (d.text) {
            textList.push(d.text)
          }
        })
        headers.push({
          text: textList.join('') || '',
          ...attrs
        })
      }
    })
  }
  headerList.value = headers
}
const scrollToHeader = (id) => {
  const element = document.querySelector(`[id="${id}"]`) // 假设每个标题都有一个唯一的id属性
  if (element) {
    // 使用浏览器原生的scrollIntoView方法，可以设置平滑滚动
    element.scrollIntoView({
      top: 600,
      behavior: 'smooth', // 平滑滚动
      block: 'start', // 顶部对齐
      inline: 'nearest' // 最近边对齐
    })
  } else {
    console.error('无法找到对应的标题元素')
  }
}
</script>

<style lang="scss">
@import './index.scss';
</style>
