import request from '@/utils/request'
import type { Response } from '@/services/types/response'
import type { CosSignResultInfo, OssSignResultInfo } from '@/services/types/appMessage'

export async function getFileCosSign(params: any): Promise<Response<CosSignResultInfo>> {
  return request.get('/userFile/getCosSign', { params })
}

export async function getImageCosSign(params: any): Promise<Response<CosSignResultInfo>> {
  return request.get('/user/picture/getCosSign', { params })
}

export async function uploadByUrl(params: any): Promise<Response<any>> {
  return request.post('/userFile/uploadByUrl', params)
}

export async function ossGetSign(params: any): Promise<Response<OssSignResultInfo>> {
  return request.get('/userFile/oss/getSign', {
    params: {
      ...params,
      filename: params?.filename ? encodeURIComponent(params.filename) : ''
    }
  })
}
