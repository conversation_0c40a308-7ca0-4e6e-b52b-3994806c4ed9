import request from '@/utils/request'
import type { Response } from '@/services/types/response'
import type {
  RepositoryFile,
  RepositoryFileGetDetailParams,
  SaveEditorDataParams,
  GetExportCodeParams
} from '@/services/types/repositoryFile'
export enum Api {
  exportWord = '/repositoryFile/exportWord'
}
export function repositoryFileGetDetail(
  params: RepositoryFileGetDetailParams
): Promise<Response<RepositoryFile>> {
  return request.get('/repositoryFile/getDetail', { params })
}
export function repositorySaveEditorData(params: SaveEditorDataParams): Promise<Response<string>> {
  return request.post('/repositoryFile/saveEditorData', params)
}
export function getExportCode(params: GetExportCodeParams): Promise<Response<string>> {
  return request.post('/repositoryFile/getExportCode', params)
}

export async function checkFileHash(params: any): Promise<Response<any>> {
  return request.post('/userFile/checkFileSha256', params)
}
export async function addFiles(params: any): Promise<Response<string>> {
  return request.post('/repositoryFile/addFiles', params)
}

export function exportWordToUrl(params: any): Promise<Response<any>> {
  return request.get('/submissionEdit/exportWordToUrl', { params })
}

export async function searchScholar(params: any): Promise<Response<any>> {
  return request.post('/scholar/search', params)
}
