import request from '@/utils/request'
import type { Response } from '@/services/types/response'
export enum Api {
  exportWord = '/submissionEdit/exportWord',
}
import type {
  SubmissionEditInfo,
  DoAiActionParams,
  SubmissionSaveParams,
  ExportCodeParams,
  } from '@/services/types/submissionEditRes'
// /ai/submissionEdit/getLast?submissionId=123 获取编辑器内容 @大董 
// /ai/submissionEdit/list?submissionId=123 查询保存纪录

// /ai/submissionEdit/save  保存
export function getLast(params: any): Promise<Response<SubmissionEditInfo>> {
  return request.get('/submissionEdit/getLast', { params })
}
export function submissionEditList(params: any): Promise<Response<any>> {
  return request.get('/submissionEdit/list', { params })
}
export function save(params: SubmissionSaveParams): Promise<Response<string>> {
  return request.post('/submissionEdit/save', params )
}

export function getExportCode(params: ExportCodeParams): Promise<Response<string>> {
  return request.post('/submissionEdit/getExportCode', params )
}

// POST /ai/submissionEdit/doAiAction 执行ai编辑动作

// @NotEmpty
// @Schema(title = "创作id")
// private String submissionId;

// @NotEmpty  动作 code  polish、expand、shorten、translate
// @Schema(title = "ai动作")
// private String code;

// @NotEmpty
// @Schema(title = "需要处理的内容")
// private String content;

// @Schema(title = "扩展参数")
// private Map<String, String> params;
export function doAiAction(params: DoAiActionParams): Promise<Response<string>> {
  return request.post('/submissionEdit/doAiAction', params )
}