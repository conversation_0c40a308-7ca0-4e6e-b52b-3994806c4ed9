import { defineStore } from 'pinia'

import type { AppUserInfo, KnowledgeAssistantMemberInfo } from '@/services/types/loginMobileRes'
import type { LastUseAppInfo } from '@/services/types/appMessage'
import type { TeamInfo } from '@/services/types/loginMobileRes'
import { storage } from '@/utils/local-storage'

interface IUserStoreState {
  isLoadedInitialDataFromServer: boolean
  currentLoginInfo?: AppUserInfo
  knowledgeAssistantMemberInfo: KnowledgeAssistantMemberInfo | null
  clickId: string
  channel: string
  sourceId: string
  openRecordUrl: string
  recentAppList: LastUseAppInfo[]
  currentTeam: TeamInfo | null
}

// 获取本地存储的团队信息
export const getLocalStorageTeamInfo = (): TeamInfo | null => {
  try {
    const storedTeam = storage.get('team_info')

    console.log(storedTeam, 'storedTeam')

    // 验证团队信息的有效性
    if (
      storedTeam &&
      typeof storedTeam === 'object' &&
      'id' in storedTeam &&
      'name' in storedTeam
    ) {
      return storedTeam as TeamInfo
    }

    return null
  } catch (error) {
    console.error('Error getting team info from localStorage:', error)
    return null
  }
}

export const useUserStore = defineStore({
  id: 'user',
  state: (): IUserStoreState => ({
    currentTeam: getLocalStorageTeamInfo(),
    knowledgeAssistantMemberInfo: null as KnowledgeAssistantMemberInfo | null,
    isLoadedInitialDataFromServer: false,
    clickId: '',
    channel: '',
    sourceId: '',
    openRecordUrl: '',
    recentAppList: []
  }),
  getters: {
    // 获取团队ID
    getTeamId(): string {
      return this.currentTeam?.id || ''
    },

    userNicknameHint: (state) => {
      if (!state.currentLoginInfo) {
        return ''
      }
      return state.currentLoginInfo?.nickname
    }
  },
  actions: {
    loadRecentAppListFromLocalStorage() {
      const recentAppListJSON = localStorage.getItem('recentAppList')
      if (recentAppListJSON) {
        this.recentAppList = JSON.parse(recentAppListJSON)
      }
    },
    setKnowledgeAssistantMemberInfo(info: any) {
      this.knowledgeAssistantMemberInfo = info
    },
    addRecentApp(app: LastUseAppInfo) {
      const existingIndex = this.recentAppList.findIndex(
        (item) => item.code === app.code && item.type === app.type
      )
      if (existingIndex !== -1) {
        this.recentAppList.splice(existingIndex, 1)
      }

      const newAppInfo: LastUseAppInfo = {
        ...app,
        lastTime: new Date().toISOString()
      }

      this.recentAppList.unshift(newAppInfo)

      // 保证记录最多只有10条
      if (this.recentAppList.length > 10) {
        this.recentAppList.length = 10
      }

      // 保存到localStorage
      localStorage.setItem('recentAppList', JSON.stringify(this.recentAppList))
    },
    mergeFromServer(serverRecentAppList: LastUseAppInfo[]) {
      // 创建一个映射以跟踪已处理的代码和类型
      const processedMap = new Map<string, boolean>()

      // 创建一个新数组来存储合并后的数据
      const mergedRecentAppList: LastUseAppInfo[] = []

      // 首先处理服务器上的最近应用记录
      for (const serverApp of serverRecentAppList) {
        mergedRecentAppList.push(serverApp)
        processedMap.set(`${serverApp.code}-${serverApp.type}`, true)
      }

      // 然后添加本地的最近应用记录，如果它们还没有被处理
      for (const localApp of this.recentAppList) {
        if (!processedMap.has(`${localApp.code}-${localApp.type}`)) {
          mergedRecentAppList.push(localApp)
        }
      }

      // 如果合并后的列表超过10条，减少到10条
      if (mergedRecentAppList.length > 10) {
        mergedRecentAppList.length = 10
      }

      // 更新本地的最近应用记录列表和localStorage
      this.recentAppList = mergedRecentAppList
      localStorage.setItem('recentAppList', JSON.stringify(mergedRecentAppList))
    }
  }
})
