import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { isXiaoin } from '@/utils/utils'
import { useUserStore } from './user'
import { buyAcademicSearch, getTeamCoinBalance } from '@/api/user'
import { UserService } from '@/services/user'

// 定义兑换选项类型
type ExchangeOption = 10 | 50 | 100 | 'custom'

// 定义 store 的状态接口
interface IExchangeSearchState {
  // 选中的选项索引
  selectedOptionIndex: ExchangeOption
  // 自定义次数
  customTimes: number
  // 是否正在兑换
  isExchanging: boolean
  // 用户硬币余额
  userCoinBalance: number
  // 每次搜索消耗的硬币数
  coinPerSearch: number
}

// 使用对象语法定义 store
export const useExchangeSearchStore = defineStore({
  id: 'exchangeSearch',

  state: (): IExchangeSearchState => ({
    selectedOptionIndex: 10,
    customTimes: 1,
    isExchanging: false,
    userCoinBalance: 0, // 默认余额，实际应从API获取
    coinPerSearch: 2000 // 每次搜索消耗100硬币
  }),

  getters: {
    // 获取选中的次数
    selectedTimes: (state): number => {
      if (state.selectedOptionIndex === 'custom') {
        return state.customTimes
      }
      return state.selectedOptionIndex as number
    },

    // 计算所需硬币数
    requiredCoins: (state): number => {
      const times =
        state.selectedOptionIndex === 'custom'
          ? state.customTimes
          : (state.selectedOptionIndex as number)
      return times * state.coinPerSearch
    },

    // 判断硬币是否不足
    isInsufficientCoins(): boolean {
      return this.userCoinBalance < this.requiredCoins
    }
  },

  actions: {
    // 打开兑换弹窗
    openExchangeModal() {
      // 这里可以添加打开弹窗时的逻辑，比如获取最新的用户余额
      this.fetchUserCoinBalance()
    },

    // 关闭兑换弹窗
    closeExchangeModal() {
      // 重置状态
      this.selectedOptionIndex = 10
      this.customTimes = 1
      this.isExchanging = false
    },

    // 选择选项
    selectOption(option: ExchangeOption) {
      this.selectedOptionIndex = option
    },

    // 设置自定义次数
    setCustomTimes(times: number) {
      this.customTimes = Math.max(1, Math.min(99999999, times))
    },

    // 获取用户硬币余额
    async fetchUserCoinBalance() {
      try {
        const store = useUserStore()

        // 这里应该调用实际的API获取用户余额
        const teamCoinBalance = await this.getTeamCoinBalance()

        if (store.getTeamId) {
          this.userCoinBalance = teamCoinBalance
          return
        }
        if (!store.currentLoginInfo) {
          this.userCoinBalance = 0
          return
        }
        const _coinBalance = store.currentLoginInfo?.coinBalance || 0
        if (_coinBalance < 0.1) {
          this.userCoinBalance = _coinBalance / 1000
          return
        }
        if (isXiaoin()) {
          this.userCoinBalance = _coinBalance
          return
        }
        if (store.getTeamId) {
          this.userCoinBalance = teamCoinBalance
          return
        }
        this.userCoinBalance = _coinBalance / 1000
        return
      } catch (error) {
        console.error('获取用户余额失败:', error)
        ElMessage.error('获取用户余额失败')
      }
    },

    async getTeamCoinBalance() {
      const store = useUserStore()
      if (!store.getTeamId) {
        return 0
      }
      const res = await getTeamCoinBalance({ teamId: store.getTeamId || '' })
      if (res.ok && res.data) {
        return res.data || 0
      }
      return 0
    },

    // 执行兑换
    async performExchange() {
      if (this.isInsufficientCoins) {
        ElMessage.error('硬币余额不足')
        return false
      }

      if (this.selectedTimes <= 0) {
        ElMessage.error('兑换次数必须大于0')
        return false
      }

      this.isExchanging = true

      try {
        // 这里应该调用实际的兑换API
        const result = await buyAcademicSearch({
          count: this.selectedTimes
        })
        if (result.ok) {
          ElMessage.success(`成功兑换${this.selectedTimes}次学术搜索次数`)

          // 更新用户信息
          await UserService.loadUserInfoAndAssistantMemberInfo()

          // 关闭模态弹窗
          this.closeExchangeModal()
          return true
        } else {
          ElMessage.error(result.message || '兑换失败，请稍后重试')
          return false
        }
      } catch (error) {
        console.error('兑换失败:', error)
        ElMessage.error('兑换失败，请重试')
        return false
      } finally {
        this.isExchanging = false
      }
    }
  }
})
