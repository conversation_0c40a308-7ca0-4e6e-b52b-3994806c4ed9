import { defineStore } from 'pinia'
import { ACTION_CODE } from '@/utils/constants'

// 定义支付信息接口
interface PaymentInfo {
  title: string
  needPayCoins: number
  isNeedRecharge: boolean
  isNeedUpgrade: boolean
}

// 定义 store 的状态接口
interface IModalState {
  // 当前触发的动作代码
  currentActionCode: string
  referceText: string

  schematicReferceText: string
  referceBeforeText: string
  referceAfterText: string

  // 弹窗可见性状态
  mathGenerateVisible: boolean
  tableGenerateVisible: boolean
  chartGenerateVisible: boolean
  imgGenerateVisible: boolean
  schematicGenerateVisible: boolean
  // 支付弹窗状态
  payModalVisible: boolean
  // 需要支付的硬币数量等信息
  paymentInfo: PaymentInfo
}

// 使用对象语法定义 store
export const useModalStore = defineStore({
  id: 'modal',

  state: (): IModalState => ({
    currentActionCode: '',
    referceText: '',
    schematicReferceText: '',
    referceBeforeText: '',
    referceAfterText: '',
    mathGenerateVisible: false,
    tableGenerateVisible: false,
    chartGenerateVisible: false,
    imgGenerateVisible: false,
    schematicGenerateVisible: false,
    payModalVisible: false,
    paymentInfo: {
      title: '确认支付',
      needPayCoins: 10000,
      isNeedRecharge: false,
      isNeedUpgrade: false
    }
  }),

  getters: {
    // 判断当前是否是特定动作
    isCurrentAction:
      (state) =>
      (actionCode: string): boolean => {
        return state.currentActionCode === actionCode
      }
  },

  actions: {
    // 打开指定类型的弹窗
    openModal(actionCode: string) {
      this.currentActionCode = actionCode

      // 根据不同的动作代码打开对应的弹窗
      switch (actionCode) {
        case ACTION_CODE.FORMULA:
          this.mathGenerateVisible = true
          break
        case ACTION_CODE.TABLE:
          this.tableGenerateVisible = true
          break
        case ACTION_CODE.CHART:
          this.chartGenerateVisible = true
          break
        case ACTION_CODE.IMAGE:
          this.imgGenerateVisible = true
          break
        case ACTION_CODE.SCHEMATIC:
          this.schematicGenerateVisible = true
          break
      }
    },

    // 关闭所有弹窗
    closeAllModals() {
      this.mathGenerateVisible = false
      this.tableGenerateVisible = false
      this.chartGenerateVisible = false
      this.imgGenerateVisible = false
      this.schematicGenerateVisible = false
      this.payModalVisible = false
    },

    closeReferceText() {
      this.referceText = ''
      this.schematicReferceText = ''
      this.referceBeforeText = ''
      this.referceAfterText = ''
    },

    setReferceText(text: string) {
      this.referceText = text
    },

    setSchematicReferceText(text: string) {
      this.schematicReferceText = text
    },

    setReferceBeforeText(text: string) {
      this.referceBeforeText = text
    },

    setReferceAfterText(text: string) {
      this.referceAfterText = text
    },

    // 打开支付弹窗
    openPayModal() {
      // 根据不同的actionCode可以设置不同的支付信息
      this.payModalVisible = true
    },

    // 关闭支付弹窗
    closePayModal() {
      this.payModalVisible = false
    }
  }
})
