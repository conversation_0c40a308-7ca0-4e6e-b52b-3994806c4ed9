// @ts-nocheck
import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import NotFound from '@/components/NotFound.vue' // 导入 NotFound 组件

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/HomeView.vue')
    },

    {
      path: '/history', // :id 是路由的动态参数
      name: 'history',
      component: () => import('@/views/HistoryView/index.vue')
    },

    {
      path: '/my-knowledge-note',
      name: 'myKnowledgeNote',
      component: () => import('../views/MyKnowledgeNote.vue')
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue')
    },
    // 404 路由（catch-all）
    {
      path: '/:pathMatch(.*)*', // 使用 catch-all 匹配任何未定义的路径
      name: 'NotFound',
      component: NotFound
    }
  ]
})

export default router
