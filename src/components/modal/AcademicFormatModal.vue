<template>
  <el-dialog
    v-model="visible"
    title="选择文献格式"
    width="600px"
    :show-close="!loading"
    :close-on-click-modal="!loading"
    :close-on-press-escape="!loading"
    :before-close="handleBeforeClose"
  >
    <!-- 副标题 -->
    <div class="format-subtitle">使用专业文献格式，一键优化文末参考文献</div>

    <!-- 选项卡区域 -->
    <div class="format-options">
      <el-button-group>
        <el-button
          v-for="format in formatOptions"
          :key="format.value"
          class="format-option"
          :class="{ 'format-option-active': formState.format === format.value }"
          @click="selectFormat(format.value)"
        >
          {{ format.label }}
        </el-button>
      </el-button-group>
    </div>

    <!-- 示例区域 -->
    <div class="format-example" v-if="formState.format == formatOptions[0].value">
      <div class="example-content">示例:</div>
      <div class="example-content">
        [1]
        张晓蓓,高逸文.父母外出务工对农村留守儿童心理及行为的影响研究[J].长沙理工大学学报(社会科学版),2025,40(04):143-154.DOI:10.16573/j.cnki.1672-934x.2025.04.0014.<br />
        [2]
        陆芳,翟友华.家庭累积风险对单亲儿童心理发展的影响研究[J].盐城师范学院学报(人文社会科学版),2025,45(04):1-10.DOI:10.16401/j.cnki.ysxb.1003-6873.2025.04.040.<br />
        [3]
        胡佩瑾,宋逸.推动儿童青少年心理健康从“疾病发现”向“健康促进”转变[J].中国卫生,2025,(07):90-91.DOI:10.15973/j.cnki.cn11-3708/d.2025.07.049.
      </div>
    </div>
    <div class="format-example" v-if="formState.format == formatOptions[1].value">
      <div class="example-content">示例:</div>
      <div class="example-content">
        [1] 张晓蓓,and
        高逸文."父母外出务工对农村留守儿童心理及行为的影响研究."长沙理工大学学报(社会科学版)
        40.04(2025):143-154.doi:10.16573/j.cnki.1672-934x.2025.04.0014.<br />
        [2] 陆芳,and
        翟友华."家庭累积风险对单亲儿童心理发展的影响研究."盐城师范学院学报(人文社会科学版)
        45.04(2025):1-10.doi:10.16401/j.cnki.ysxb.1003-6873.2025.04.040.<br />
        [3] 胡佩瑾,and 宋逸."推动儿童青少年心理健康从“疾病发现”向“健康促进”转变."中国卫生
        .07(2025):90-91.doi:10.15973/j.cnki.cn11-3708/d.2025.07.049.
      </div>
    </div>
    <div class="format-example" v-if="formState.format == formatOptions[2].value">
      <div class="example-content">示例:</div>
      <div class="example-content">
        [1] 张晓蓓 &
        高逸文.(2025).父母外出务工对农村留守儿童心理及行为的影响研究.长沙理工大学学报(社会科学版),40(04),143-154.doi:10.16573/j.cnki.1672-934x.2025.04.0014.<br />
        [2] 陆芳 &
        翟友华.(2025).家庭累积风险对单亲儿童心理发展的影响研究.盐城师范学院学报(人文社会科学版),45(04),1-10.doi:10.16401/j.cnki.ysxb.1003-6873.2025.04.040.<br />
        [3] 胡佩瑾 &
        宋逸.(2025).推动儿童青少年心理健康从“疾病发现”向“健康促进”转变.中国卫生,(07),90-91.doi:10.15973/j.cnki.cn11-3708/d.2025.07.049.
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleOk" :loading="loading" :disabled="loading">
          {{ loading ? '处理中...' : '确认' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, watch, computed } from 'vue'

const props = defineProps<{
  modelValue: boolean
  loading?: boolean
  submissionId?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { format: string }): void
  (e: 'setLoading', value: boolean): void
}>()

// 使用计算属性获取 loading 状态
const loading = computed(() => props.loading || false)

const formState = ref({
  format: 'GB/T 7714'
})

// 计算属性控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式选项配置
const formatOptions = ref([
  { value: 'GB/T 7714', label: 'GB/T 7714' },
  { value: 'MLA', label: 'MLA' },
  { value: 'APA', label: 'APA' }
])

// 选择格式方法
const selectFormat = (format: string) => {
  formState.value.format = format
}

// 监听 modelValue 变化，当弹窗打开时重置表单
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      formState.value = {
        format: 'GB/T 7714'
      }
    }
  }
)

// 处理弹窗关闭前的检查
const handleBeforeClose = (done: () => void) => {
  if (loading.value) {
    ElMessage.warning('正在整理文献格式，请稍候')
    return
  }
  done()
}

const handleOk = async () => {
  if (loading.value) {
    ElMessage.warning('正在整理文献格式，请稍候')
    return
  }
  emit('confirm', { format: formState.value.format })
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在整理文献格式，请稍候')
    return
  }
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.format-subtitle {
  color: #777777;
  font-size: 15px;
  //   margin-bottom: 20px;
  margin-top: 10px;
}

.format-options {
  display: flex;
  margin-bottom: 20px;
  font-size: 16px;
  padding-top: 10px;
  //   padding-bottom: 10px;

  @media (max-width: 640px) {
    flex-direction: column;
  }

  // 覆盖 el-button-group 默认样式
  :deep(.el-button-group) {
    display: flex;
    width: 100%;

    @media (max-width: 640px) {
      flex-direction: column;
    }
  }

  // 覆盖 el-button 默认样式
  :deep(.el-button) {
    position: relative;
    flex: 1;
    padding: 16px 16px;
    border: 1px solid #e3eafb;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    background-color: #f5f7ff;
    color: #333333;
    margin: 0;
    border-radius: 0;

    // 第一个按钮左侧圆角
    &:first-child {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;

      @media (max-width: 640px) {
        border-top-right-radius: 6px;
        border-bottom-left-radius: 0;
      }
    }

    // 最后一个按钮右侧圆角
    &:last-child {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;

      @media (max-width: 640px) {
        border-bottom-left-radius: 6px;
        border-top-right-radius: 0;
      }
    }

    // 中间按钮在移动端的圆角处理
    @media (max-width: 640px) {
      &:not(:first-child):not(:last-child) {
        border-radius: 0;
      }
    }

    // 移除相邻按钮间的重复边框
    &:not(:first-child) {
      border-left: none;

      @media (max-width: 640px) {
        border-left: 1px solid #e3eafb;
        border-top: none;
      }
    }

    &:hover {
      border-color: #2551b5;
      z-index: 1;
      border: 1px solid #2551b5;
    }

    &.format-option-active {
      border-color: #2551b5;
      background-color: #e7edfe;
      color: #2551b5;
      z-index: 2;
      border: 1px solid #2551b5;
    }

    // 移除 Element Plus 默认的 focus 样式
    &:focus {
      outline: none;
      box-shadow: none;
    }
  }
}

.format-example {
  position: relative;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
}

.example-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.example-content {
  font-size: 14px;
  line-height: 27px;
  color: #333333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
