<template>
  <!---支付弹窗-->
  <el-dialog v-model="visible" title="支付确认" width="450px" append-to-body @close="handleClose">
    <div class="payment-container" v-loading="loading">
      <!-- <div>
        <div class="upgrade-card" @click="handleUpgradeVip">
          <div class="upgrade-text">升级会员，免费送编辑次数</div>
          <div class="upgrade-button">
            去升级
            <RightOutlined color="#ff4242" font-size="15px" />
          </div>
        </div>
        <div class="fee-card">
          <div class="fee-label">本次编辑费用：</div>
          <div class="fee-amount">{{ formatCoinAmount(payData.needPayCoins, 0) }}硬币</div>
        </div>
      </div> -->
      <div>
        <div class="fee-card">
          <div class="fee-label">写作费用：</div>
          <div class="fee-amount">{{ formatCoinAmount(payData.needPayCoins, 0) }}硬币</div>
        </div>
      </div>

      <div class="balance-card">
        <img
          src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png"
          style="width: 20px; height: 20px"
        />
        <span class="balance-text">剩余硬币：{{ coinBalance }}</span>
      </div>

      <div class="actions-container">
        <button v-if="isNeedRechargeCoinsOrVip" @click="handleRecharge" class="action-button">
          充值
        </button>

        <button v-else @click="handleCreate" class="action-button">确认</button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import Iconfont from '@/components/Iconfont.vue'
import { formatCoinAmount, isXiaoin } from '@/utils/utils'
import { computed, onMounted, ref, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { TEAM_MEMBER_ROLE } from '@/utils/constants'

import { useModalStore } from '../../stores/modalStore'
import { ElMessage } from 'element-plus'
import { getTeamCoinBalance, getUserInfo } from '@/api/user'
import { UserService } from '@/services/user'

const userStore = useUserStore()
const modalStore = useModalStore()

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'recharge'])

const loading = ref(false)
const visible = ref(props.modelValue)

const teamCoinBalance = ref(0)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    // 当弹窗打开时，设置loading为true
    if (val) {
      loading.value = true
      // 模拟数据加载，3秒后关闭loading
      setTimeout(() => {
        loading.value = false
      }, 1000)
    }
  },
  { immediate: true } // 立即执行一次
)

// 同步visible到modelValue
watch(
  () => visible.value,
  (val) => {
    emit('update:modelValue', val)
  }
)

// 获取支付数据
const payData = computed(() => {
  return modalStore.paymentInfo
})

// 处理关闭弹窗
const handleClose = () => {
  modalStore.closePayModal()
  visible.value = false
}

// 检查是否需要充值
const getIsTeamIdentity = computed(() => userStore.getTeamId)

const coinBalance = computed(() => {
  if (getIsTeamIdentity.value) {
    return teamCoinBalance.value // 团队硬币余额示例值
  }
  if (!userStore.currentLoginInfo) {
    return 0
  }
  const _coinBalance = userStore.currentLoginInfo?.coinBalance || 0
  if (_coinBalance < 0.1) {
    return _coinBalance
  }
  if (isXiaoin()) {
    return _coinBalance
  }
  if (getIsTeamIdentity.value) {
    return teamCoinBalance.value
  }
  return _coinBalance / 1000
})

// const knowledgeAssistantMemberInfo = computed(() => {
//   return userStore.knowledgeAssistantMemberInfo
// })

const isNeedRechargeCoinsOrVip = computed((): boolean => {
  //
  // if (!getIsTeamIdentity.value && (knowledgeAssistantMemberInfo.value?.vipLevel || 0) < 1) {
  //   return coinBalance.value - payData.value.needPayCoins < 0
  // }
  // 其他会员或团队会员
  return coinBalance.value - payData.value.needPayCoins < 0
})

// VIP升级处理
// const handleUpgradeVip = () => {
//   // 跳转到VIP升级页面或打开升级弹窗
//   console.log('跳转到VIP升级页面')
//   emit('recharge', 2)
// }

// 充值处理
const handleRecharge = () => {
  if (getIsTeamIdentity.value) {
    if (userStore.currentTeam?.member.role == TEAM_MEMBER_ROLE.OWNER) {
      emit('recharge', 1)
    } else {
      ElMessage.warning('请联系团队创建者充值')
    }
    return
  }
  emit('recharge', 1)
}

// 确认创建处理
const handleCreate = () => {
  visible.value = false
  modalStore.closePayModal()
  emit('confirm')
}

const loadTeamCoinData = async () => {
  if (!userStore.getTeamId) {
    return
  }
  const res = await getTeamCoinBalance({ teamId: userStore.getTeamId || '' })
  console.log('res data ==>', res)
  if (res.ok && res.data) {
    teamCoinBalance.value = res.data || 0

    // coinBalance.value = res.data || 0
  }
}

// 组件挂载时的处理
onMounted(async () => {
  if (!userStore.getTeamId) {
    await UserService.loadUserInfo()
    return
  }

  // 初始加载状态已经通过watch处理，这里不需要重复设置
  await loadTeamCoinData()
})
</script>

<style lang="scss" scoped>
.payment-container {
  padding: 1.5rem 0.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: center;
  font-size: 0.875rem;
  // min-height: 300px;

  .upgrade-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem 1.25rem;
    background-color: #f5f7ff;
    border: 1px solid #c3d5fd;
    border-radius: 0.625rem;
    margin-bottom: 1.25rem;

    .upgrade-text {
      color: #2551b5;
      font-size: 0.9375rem;
    }

    .upgrade-button {
      color: #ff2442;
      font-size: 0.9375rem;
    }
  }

  .fee-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    background-color: #f5f7ff;
    border-radius: 0.625rem;

    .fee-label {
      color: #777777;
      font-size: 0.9375rem;
    }

    .fee-amount {
      color: #333333;
      font-size: 0.9375rem;
    }
  }

  .balance-card {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background-color: #ebf5ff;
    border-radius: 0.5rem;
    text-align: left;
    color: #4a5568;

    .balance-text {
      padding-left: 0.25rem;
    }
  }

  .actions-container {
    padding-top: 1rem;

    .action-button {
      padding: 0.5rem 2rem;
      border-radius: 0.5rem;
      transition: all 0.2s;
      white-space: nowrap;
      background-color: #3b82f6;
      color: white;
      font-size: 1rem;
      cursor: pointer;
      border: none;

      &:hover {
        background-color: #2563eb;
      }
    }
  }
}

// 保留原有的 VIP 模态框样式（以备可能的后续使用）
.vip-container {
  padding: 0 16px;

  .vip-title {
    text-align: center;
    margin-bottom: 16px;

    h4 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .feature-desc {
      color: #666;
      font-size: 14px;
    }
  }

  .card-container {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 16px;

    .card {
      flex: 1;
      border: 1px solid #eee;
      border-radius: 8px;
      padding: 16px 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }

      &.card-active {
        border-color: #409eff;
        box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .price-wrapper {
    text-align: center;

    .price-wrap {
      display: flex;
      justify-content: center;
      align-items: baseline;

      .price-amount {
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }

      .price-unit {
        font-size: 14px;
        color: #666;
        margin-left: 2px;
      }
    }

    .price-tag {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }

  .buttons-container {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-top: 20px;

    .cancel-button,
    .confirm-button {
      flex: 1;
    }
  }
}
</style>
