<template>
  <div>
    <el-dialog
      v-model="visible"
      title="AI生成示意图"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <!-- <div class="space-y-4"> -->
      <el-form layout="vertical">
        <div class="reference-text" v-if="modalStore.referceText">
          <div class="text-ellipsis-wrapper">
            <div class="text-ellipsis">
              {{ modalStore.referceText }}
            </div>
          </div>
        </div>

        <el-form-item required>
          <el-input
            v-model="prompt"
            type="textarea"
            :rows="8"
            placeholder="请输入您需要生成的示意图内容或补充要求"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <!-- </div> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleOk" :loading="loading">生 成</el-button>
        </div>
      </template>
    </el-dialog>

    <PayModal
      v-if="modalStore.payModalVisible"
      v-model:model-value="modalStore.payModalVisible"
      @recharge="onRecharge"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ACTION_CODE } from '@/utils/constants'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '@/services/user'

import { useUserStore } from '@/stores/user'
import { generateSchematic } from '@/api/user'
import { useModalStore } from '../../stores/modalStore'
import PayModal from '@/components/modal/PayModal.vue'
import { ElMessage } from 'element-plus'
import { useSchematicSvgStore } from '@/stores/schematicSvgStore'

const userStore = useUserStore()
const modalStore = useModalStore()
const schematicSvgStore = useSchematicSvgStore()

const props = defineProps<{
  modelValue: boolean
  vipLevel?: number
  submissionId: string
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', content: {}): void
  (e: 'recharge', value: number): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const prompt = ref('')
const loading = ref(false)

const sumbitData = async () => {
  if (!props.submissionId) {
    ElMessage.error('示意图生成错误')
    return
  }
  loading.value = true
  try {
    let userContext = ''
    if (modalStore.schematicReferceText) {
      userContext = `${modalStore.schematicReferceText}, \n 请结合上下文生成示意图`
    }
    const params: any = {
      submissionId: props.submissionId,
      code: ACTION_CODE.SCHEMATIC,
      content: userContext || prompt.value,
      params: {
        title: props.title,
        before: modalStore.referceBeforeText,
        after: modalStore.referceAfterText
      }
    }
    if (userStore.getTeamId) {
      params.teamId = userStore.getTeamId
    }
    const response = await generateSchematic(params)
    // console.log('response ==>', response)
    if (response.ok && response.code == 200) {
      // 设置示意图数据
      schematicSvgStore.setSchematicData(response.data)
      // 打开示意图预览弹窗
      schematicSvgStore.openModal()

      // 加载用户信息
      await UserService.loadUserInfoAndAssistantMemberInfo()

      // 关闭当前弹窗
      clearContent()
    }
  } catch (error) {
    console.error('生成示意图失败:', error)
    ElMessage.error('生成失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleOk = async () => {
  if (!modalStore.referceText && !prompt.value) {
    ElMessage.warning('请选择参考文本或输入内容')
    return
  }
  // 打开支付弹窗
  modalStore.openPayModal()
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在创作中，请勿关闭弹窗')
    return
  }
  clearContent()
}

const onRecharge = () => {
  emit('recharge', 1)
}

const onConfirm = () => {
  sumbitData()
}

const clearContent = () => {
  modalStore.closeAllModals()
  modalStore.closeReferceText()
  visible.value = false
  prompt.value = ''
}

onMounted(() => {})
onBeforeUnmount(() => {
  clearContent()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.reference-text {
  margin: 1.25rem 0;
  padding: 0.75rem 1.25rem;
  background-color: #ebf5ff;
  border-radius: 0.5rem;
  color: #4a5568;
}

.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.chart-type-label {
  white-space: nowrap;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}
</style>
