<template>
  <el-dialog
    v-model="visible"
    :title="modalTitle"
    @cancel="handleCancel"
    width="800px"
    destroy-on-close
  >
    <el-form :model="localEditingData" layout="vertical">
      <el-form-item label="标签" required>
        <el-input v-model="localEditingData.labels" placeholder="请输入标签，用逗号分隔" />
      </el-form-item>

      <template v-if="localChartType === 'pie'">
        <!-- 饼图只显示单个数据集 -->
        <el-form-item label="数据" required>
          <el-input
            v-model="localEditingData.datasets[0].data"
            placeholder="请输入数据，用逗号分隔"
          />
        </el-form-item>
      </template>
      <template v-else>
        <el-button type="primary" @click="addTab" style="margin-bottom: 10px">
          添加数据集
        </el-button>
        <!-- 柱状图和折线图支持多个数据集 -->
        <el-tabs v-model="activeTabKey" type="card" closable @tab-remove="removeTab">
          <el-tab-pane
            v-for="(dataset, index) in localEditingData.datasets"
            :key="index"
            :label="`数据集 ${index + 1}`"
            :name="index"
          >
            <el-form-item label="数据集名称">
              <el-input v-model="dataset.label" placeholder="请输入数据集名称" />
            </el-form-item>
            <el-form-item label="数据" required>
              <el-input v-model="dataset.data" placeholder="请输入数据，用逗号分隔" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-form>
    <div class="mt-4">
      <h3 class="font-bold mb-2">预览</h3>
      <div class="h-[200px]">
        <Bar v-if="localChartType === 'bar'" :data="previewChartData" :options="chartOptions" />
        <Line v-if="localChartType === 'line'" :data="previewChartData" :options="chartOptions" />
        <Pie v-if="localChartType === 'pie'" :data="previewChartData" :options="chartOptions" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleOk">生 成</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from 'chart.js'
import { computed, ref, watch } from 'vue'
import { Bar, Line, Pie } from 'vue-chartjs'

// 注册ChartJS组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

interface Dataset {
  label: string
  data: string
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  tension?: number
  fill?: boolean
}

interface EditingData {
  labels: string
  datasets: Dataset[]
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    default: 'bar',
    validator: (value: string) => ['bar', 'line', 'pie'].includes(value)
  },
  editingData: {
    type: Object as () => EditingData,
    default: () => ({
      labels: '',
      datasets: [
        {
          label: '',
          data: '',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgb(75, 192, 192)',
          borderWidth: 1,
          tension: 0.1,
          fill: false
        }
      ]
    })
  }
})

const emit = defineEmits(['update:visible', 'save', 'cancel'])
const visible = computed({
  get: () => props.visible,
  set: (val) => {
    emit('update:visible', val)
  }
})

// 本地状态
const localChartType = ref(props.chartType)
const localEditingData = ref<EditingData>({ ...props.editingData })
const activeTabKey = ref(0)

// 当外部数据变化时，更新本地状态
watch(
  () => props.chartType,
  (newVal) => {
    localChartType.value = newVal
  }
)

watch(
  () => props.editingData,
  (newVal) => {
    localEditingData.value = { ...newVal }
  },
  { deep: true }
)

// 当visible变化时，重置本地状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      localChartType.value = props.chartType
      // 如果是饼图，确保只有一个数据集
      if (props.chartType === 'pie') {
        localEditingData.value = {
          labels: props.editingData.labels,
          datasets: [
            {
              label: '',
              data: props.editingData.datasets[0]?.data || ''
            }
          ]
        }
      } else {
        localEditingData.value = { ...props.editingData }
      }
      activeTabKey.value = 0
    }
  }
)

// 计算弹窗标题
const modalTitle = computed(() => {
  const typeMap = {
    bar: '柱状图',
    line: '折线图',
    pie: '饼图'
  }
  return `编辑${typeMap[localChartType.value as keyof typeof typeMap]}数据`
})

// 图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false
}

// 定义颜色方案常量
const CHART_COLORS = [
  { bg: 'rgba(75, 192, 192, 0.2)', border: 'rgb(75, 192, 192)' }, // 绿色
  { bg: 'rgba(153, 102, 255, 0.2)', border: 'rgb(153, 102, 255)' }, // 紫色
  { bg: 'rgba(255, 99, 132, 0.2)', border: 'rgb(255, 99, 132)' }, // 红色
  { bg: 'rgba(54, 162, 235, 0.2)', border: 'rgb(54, 162, 235)' }, // 蓝色
  { bg: 'rgba(255, 205, 86, 0.2)', border: 'rgb(255, 205, 86)' } // 黄色
]

const addTab = (targetName: string) => {
  onTabEdit(targetName, 'add')
}

const removeTab = (targetName: string) => {
  onTabEdit(targetName, 'remove')
}
// 处理标签页编辑
const onTabEdit = (
  targetKey: string | number | MouseEvent | KeyboardEvent,
  action: 'add' | 'remove'
) => {
  // 饼图不支持编辑数据集
  if (localChartType.value === 'pie') return

  if (action === 'add') {
    const newDataset: Dataset = {
      label: '',
      data: '',
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      borderColor: 'rgb(75, 192, 192)',
      borderWidth: 1,
      tension: 0.1,
      fill: false
    }
    localEditingData.value.datasets.push(newDataset)
    activeTabKey.value = localEditingData.value.datasets.length - 1
  } else if (action === 'remove' && localEditingData.value.datasets.length > 1) {
    const index = typeof targetKey === 'number' ? targetKey : Number(targetKey)
    localEditingData.value.datasets.splice(index, 1)
    activeTabKey.value = Math.min(index, localEditingData.value.datasets.length - 1)
  }
}

// 计算预览数据
const previewChartData = computed(() => {
  const labels = localEditingData.value.labels.split(',').map((item: string) => item.trim())

  if (localChartType.value === 'pie') {
    // 饼图只使用第一个数据集
    const dataset = localEditingData.value.datasets[0]
    const data = dataset.data.split(',').map((item: string) => Number(item.trim()))
    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: CHART_COLORS.map((color) => color.border)
        }
      ]
    }
  }

  return {
    labels,
    datasets: localEditingData.value.datasets.map((dataset, index) => {
      const colorIndex = index % CHART_COLORS.length
      const color = CHART_COLORS[colorIndex]

      return {
        label: dataset.label || '数据集',
        data: dataset.data.split(',').map((item: string) => Number(item.trim())),
        backgroundColor: localChartType.value === 'bar' ? color.bg : undefined,
        borderColor: color.border,
        borderWidth: localChartType.value === 'bar' ? 1 : undefined,
        tension: localChartType.value === 'line' ? 0.1 : undefined,
        fill: localChartType.value === 'line' ? false : undefined
      }
    })
  }
})

// 处理确认按钮点击
const handleOk = () => {
  const saveData = {
    chartType: localChartType.value,
    labels: localEditingData.value.labels,
    datasets:
      localChartType.value === 'pie'
        ? [
            {
              label: '',
              data: localEditingData.value.datasets[0].data
                .split(',')
                .map((item: string) => Number(item.trim())),
              backgroundColor: CHART_COLORS.map((color) => color.border)
            }
          ]
        : localEditingData.value.datasets.map((dataset, index) => {
            const colorIndex = index % CHART_COLORS.length
            const color = CHART_COLORS[colorIndex]

            return {
              label: dataset.label || '数据集',
              data: dataset.data.split(',').map((item: string) => Number(item.trim())),
              backgroundColor: localChartType.value === 'bar' ? color.bg : undefined,
              borderColor: color.border,
              borderWidth: localChartType.value === 'bar' ? 1 : undefined,
              tension: localChartType.value === 'line' ? 0.1 : undefined,
              fill: localChartType.value === 'line' ? false : undefined
            }
          })
  }

  emit('save', saveData)
  emit('update:visible', false)
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
/* 图表容器样式 */
</style>
