<template>
  <div>
    <el-dialog
      v-model="visible"
      title="AI图片生成"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <el-form layout="vertical">
        <!-- 引用内容 -->
        <div class="reference-text" v-if="modalStore.referceText">
          <div class="text-ellipsis-wrapper">
            <div class="text-ellipsis">
              {{ modalStore.referceText }}
            </div>
          </div>
        </div>

        <div class="ratio-selector">
          <span>图片比例：</span>
          <el-radio-group v-model="ratio">
            <el-radio-button :value="item" v-for="item in ratioList" :key="item">{{
              item
            }}</el-radio-button>
          </el-radio-group>
        </div>

        <el-form-item>
          <el-input
            v-model="prompt"
            placeholder="请输入你想要生成的图片内容或补充要求"
            type="textarea"
            :rows="8"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleOk" :loading="loading">生 成</el-button>
        </div>
      </template>
    </el-dialog>

    <PayModal
      v-if="modalStore.payModalVisible"
      v-model:model-value="modalStore.payModalVisible"
      @recharge="onRecharge"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '@/services/user'
import PayModal from '@/components/modal/PayModal.vue'
import { generateContent } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { useModalStore } from '../../stores/modalStore'
import { ElMessage } from 'element-plus'
import { ACTION_CODE } from '@/utils/constants'
import { isJSON } from '@/utils/utils'

const prompt = ref('')

const userStore = useUserStore()
const modalStore = useModalStore()

const props = defineProps<{
  modelValue: boolean
  vipLevel?: number
  submissionId: string
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { content: any }): void
  (e: 'recharge', code: string, value: number): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const loading = ref(false)
const ratioList = ['1:1', '3:4', '4:3', '3:2', '16:9', '9:16']
const ratio = ref(ratioList[2])

const generateImgContent = async (): Promise<any> => {
  if (!props.submissionId) {
    ElMessage.error('图片生成错误')
    return
  }

  loading.value = true
  try {
    let user_context = ''
    if (modalStore.referceText) {
      user_context = `${modalStore.referceText}, \n 请结合上下文生成一张图片`
    }
    const params: any = {
      submissionId: props.submissionId,
      code: ACTION_CODE.IMAGE,
      content: user_context || prompt.value,
      params: {
        imageRatio: ratio.value,
        title: props.title,
        ask: prompt.value
      }
    }
    if (userStore.getTeamId) {
      params.teamId = userStore.getTeamId
    }

    const response = await generateContent(params)

    if (response.ok && response.code == 200) {
      if (isJSON(response.data)) {
        const imgData = JSON.parse(response.data) as { imageUrl: string }
        return { content: imgData?.imageUrl || '' }
      }
      return { content: '' }
    } else {
      ElMessage.error('生成图片失败')
      return { content: '' }
    }
  } catch (error) {
    console.error('生成图片失败:', error)
    ElMessage.error('生成失败，请重试')
    return { content: '' }
  } finally {
    loading.value = false
  }
}
const sumbitData = async () => {
  const result = await generateImgContent()
  if (!result.content) return

  emit('confirm', {
    content: result.content
  })
  clearContent()
  await UserService.loadUserInfoAndAssistantMemberInfo()
}

const onRecharge = () => {
  emit('recharge', ACTION_CODE.IMAGE, 1)
}

const onConfirm = () => {
  sumbitData()
}

const handleOk = async () => {
  if (!modalStore.referceText && !prompt.value) {
    ElMessage.warning('请选择参考文本或输入内容')
    return
  }
  modalStore.openPayModal()
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在创作中，请勿关闭弹窗')
    return
  }
  clearContent()
}

// 监听 modelValue 变化，当弹窗打开时重置表单
watch(
  () => visible.value,
  (newValue) => {
    if (newValue) {
      prompt.value = ''
    }
  }
)

const clearContent = () => {
  modalStore.closeAllModals()
  modalStore.closeReferceText()
  // emit('update:modelValue', false)
  visible.value = false
  prompt.value = ''
}
// const handleEventConfirmCreate = () => {
//   //   if (chapterStore.payTriggerType != ACTION_CODE.IMAGE) {
//   //     return
//   //   }
//   if (modalStore.isCurrentAction(ACTION_CODE.IMAGE)) {
//     sumbitData()
//   }
// }

onMounted(() => {
  //   eventBus.on(KeyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate)
})

onBeforeUnmount(() => {
  clearContent()
  //   eventBus.off(KeyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate)
})
</script>

<style lang="scss" scoped>
.reference-text {
  margin: 1.25rem 0;
  padding: 0.75rem 1.25rem;
  background-color: #ebf5ff;
  border-radius: 0.5rem;
  color: #4a5568;
}

.ratio-selector {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 0.5rem;
}

.preview-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}

.text-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  padding: 0;
  line-height: 1.5;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
</style>
