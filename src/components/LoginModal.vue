<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :width="505"
  >
    <el-form
      :ref="
        (el: any) => {
          if (el) loginForm = el
        }
      "
      :model="form"
      label-width="80px"
      :rules="form.rules"
    >
      <el-form-item label="手机号">
        <el-input v-model="form.phone" prop="phone" autocomplete="off" maxlength="11"></el-input>
      </el-form-item>
      <el-form-item label="验证码">
        <el-input
          v-model="form.code"
          prop="code"
          autocomplete="off"
          style="width: 288px"
          maxlength="6"
        ></el-input>
        <el-button @click="getCode">获取验证码</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(loginForm)">登录</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import emitter from '@/utils/mitt'
import { isInTestServer } from '@/utils/utils'
import type { FormInstance } from 'element-plus'
import { loginMobile } from '@/api/user'
import { ElMessage } from 'element-plus'
import { onUnmounted } from 'vue'
import { UserService } from '@/services/user'

const dialogVisible = ref(false)
const loginForm = ref<FormInstance>()
emitter.on('visibleLoginModal', () => {
  console.log('on visibleLoginModal')
  setTimeout(() => {
    dialogVisible.value = true
  }, 500)
})

onUnmounted(() => {
  emitter.off('visibleLoginModal')
})
const form = reactive({
  phone: '',
  code: '',
  rules: {
    phone: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '账号不能为空', trigger: 'blur' }]
  }
})
const getCode = () => {}
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  console.log('submit!', form)
  const params = {
    mobile: form.phone,
    verifyCode: form.code
  }
  if (isInTestServer()) {
    params.verifyCode = form.code == '777778' ? '7777779' : form.code
  } else {
    params.verifyCode = form.code
  }
  const res = await loginMobile(params)
  if (!res.ok) {
    return ElMessage.error(res.message || '登录错误')
  }
  await UserService.onLoginRes(res, true)
  ElMessage({
    message: '登录成功',
    type: 'success'
  })
  handleClose()
  location.reload()
}
const handleClose = () => {
  dialogVisible.value = false
}
</script>
<style lang="scss"></style>
