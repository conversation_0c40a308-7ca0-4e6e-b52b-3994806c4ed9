<template>
  <div class="loader">
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
  </div>
</template>

<style>
.loader {
  display: flex;
  /* justify-content: center;
  align-items: center; */
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #333;
  border-radius: 50%;
  margin: 0 4px;
  animation: bounce 1s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%,
  100% {
    transform: scale(0);
    background-color: #ccc;
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    background-color: #333;
    opacity: 1;
  }
}
</style>
