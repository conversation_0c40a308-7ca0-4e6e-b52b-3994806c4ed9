<!-- src/components/Iconfont.vue -->
<template>
  <i :class="iconClass"></i>
</template>

<script>
export default {
  name: 'IconFont',
  props: {
    name: {
      type: String,
      required: true
    }
  },
  computed: {
    iconClass() {
      return `iconfont icon-${this.name}`
    }
  }
}
// @import url('//at.alicdn.com/t/c/font_4552357_bx53xe74nso.css');
// @import url('//at.alicdn.com/t/c/font_4552357_nhbpwh61db.css');
</script>
<!-- //at.alicdn.com/t/c/font_4552357_m81uug6g87h.js -->
<style scoped>
@import url('//at.alicdn.com/t/c/font_4552357_m81uug6g87h.css');
.iconfont {
  font-size: 12px;
}
</style>
