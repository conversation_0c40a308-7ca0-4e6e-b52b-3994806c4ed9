<template>
  <el-dialog
    v-model="dialogVisible"
    title=""
    width="1130"
    :before-close="handleClose"
    custom-class="payment-dialog-wrap"
    align-center
  >
    <div class="payment-dialog">
      <iframe
        :key="paymentUrl"
        :src="paymentUrl"
        style="border: none; width: 1100px; height: 670px"
        @load="iframeLoaded"
        @error="iframeError"
        :class="isLoading ? 'payment-dialog-hide' : ''"
      ></iframe>

      <div class="payment-dialog-loading" v-if="isLoading">
        <el-skeleton :rows="12" />
      </div>

      <div v-if="hasError" class="payment-dialog-error">
        <el-alert :title="errorMessage" type="error" :closable="false" show-icon />
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, ref, onMounted, watch, onUnmounted } from 'vue'
import { isLocalHost } from '@/utils/cross'

interface Props {
  modelValue: boolean
  rechargeKey: number
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  rechargeKey: 1
})

const emit = defineEmits(['update:modelValue', 'rechargeSuccess', 'operationCancel'])

const dialogVisible = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emit('update:modelValue', val)
  }
})

// 添加加载超时控制
const LOAD_TIMEOUT = 60000 // 60秒超时
let loadTimer: any

// 生成带时间戳的URL，避免缓存
const generateUrl = () => {
  const timestamp = new Date().getTime()
  const baseUrl = isLocalHost()
    ? `https://xiaoin-test.xingxiai.cn/payment`
    : `${location.protocol}//${location.hostname}/payment`
  return `${baseUrl}?rechargeKey=${props.rechargeKey}&t=${timestamp}`
}

const paymentUrl = ref(generateUrl())

const handleClose = (done: () => void) => {
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }
  done()
}

const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')

const startLoadingTimer = () => {
  if (loadTimer) {
    clearTimeout(loadTimer)
  }
  loadTimer = setTimeout(() => {
    if (isLoading.value) {
      isLoading.value = false
      hasError.value = true
      errorMessage.value = '加载超时，请重试'
      emit('operationCancel')
    }
  }, LOAD_TIMEOUT)
}

const iframeLoaded = () => {
  console.log('iframeLoaded ==>')
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }
  isLoading.value = false
  hasError.value = false
  errorMessage.value = ''
}

const iframeError = (error: any) => {
  console.log('iframeError ==>', error)
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }
  isLoading.value = false
  hasError.value = true
  errorMessage.value = '加载失败，请重试'
  emit('operationCancel')
}

// 监听 rechargeKey 变化
watch(
  () => props.rechargeKey,
  () => {
    paymentUrl.value = generateUrl()
    isLoading.value = true
    hasError.value = false
    errorMessage.value = ''
    startLoadingTimer()
  }
)

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    if (newValue) {
      paymentUrl.value = generateUrl() // 每次打开都刷新URL
      isLoading.value = true
      hasError.value = false
      errorMessage.value = ''
      startLoadingTimer()
    } else {
      if (loadTimer) {
        clearTimeout(loadTimer)
        loadTimer = null
      }
      isLoading.value = false
    }
  }
)

onMounted(() => {
  isLoading.value = true
  startLoadingTimer()
})

// 组件销毁时清理定时器
onUnmounted(() => {
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }
})
</script>
<style lang="scss" scoped>
.payment-dialog {
  position: relative;
  &-loading {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  &-hide {
    opacity: 0;
  }
  &-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    text-align: center;
  }
  &-preload {
    position: fixed;
    top: -99999px;
    left: -99999px;
  }
}
</style>
