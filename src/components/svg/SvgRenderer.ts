import type { Element } from '@svgdotjs/svg.js'
import { Text } from '@svgdotjs/svg.js'
import { defineComponent, h, onMounted, ref, type PropType } from 'vue'

const SvgRenderer = defineComponent({
  props: {
    element: {
      type: Object as PropType<Element>,
      required: true
    }
  },
  setup(props) {
    // const editingText = ref<Element | null>(null)
    // const editorVisible = ref(false)
    // const editorValue = ref('') // 添加到组件中的一个属性
    const forceUpdate = ref(0)

    // 在组件销毁时移除编辑器
    onMounted(() => {
      return () => {
        const editor = document.getElementById('svg-text-editor')
        if (editor) {
          editor.remove()
        }
      }
    })

    const renderChildren = (element: Element): any[] => {
      if (!element || !element.children || typeof element.children !== 'function') {
        return []
      }

      const children = element.children()

      return children.map((child: Element | string) => {
        if (typeof child === 'string') {
          return child
        }

        const type = child.type || child.node?.nodeName?.toLowerCase() || 'g'
        // 对attrs做浅拷贝，避免影响原始数据
        const attrs = child.attr ? { ...child.attr() } : {}

        // 如果是<use>标签，且fill存在且不为'none'，删除fill属性
        // if (type === 'use' && 'fill' in attrs && attrs.fill == '#ff00001a') {
        //   // attrs.fill = '#484848'
        // }

        // 为文本元素添加双击事件
        if (child instanceof Text) {
          return h(
            type,
            {
              ...attrs,
              ondblclick: (event: MouseEvent) => {
                event.stopPropagation()
                // handleTextDoubleClick(child)
              }
            },
            child.text()
          )
        }

        return h(type, attrs, renderChildren(child))
      })
    }

    return () => {
      // 使用forceUpdate值，即使不直接显示它，也会导致依赖它的组件重新渲染
      const _ = forceUpdate.value
      const element = props.element
      const type = element.type || element.node?.nodeName?.toLowerCase() || 'g'

      // const attrs = element.attr ? element.attr() : {}
      // 对attrs做浅拷贝，避免影响原始数据
      const attrs = element.attr ? { ...element.attr() } : {}

      // 如果是<use>标签，且fill存在且不为'none'，删除fill属性
      // if (type === 'use' && 'fill' in attrs && attrs.fill == '#ff00001a') {
      //   // attrs.fill = '#484848'
      // }

      return h(type, attrs, renderChildren(element))
    }
  }
})

export default SvgRenderer
