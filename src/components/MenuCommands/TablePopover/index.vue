<template>
  <el-popover
    :disabled="isCodeViewMode"
    placement="bottom"
    trigger="click"
    popper-class="el-tiptap-popper"
    ref="popoverRef"
    v-model="popoverVisible"
  >
    <div class="el-tiptap-popper__menu">
      <div class="el-tiptap-popper__menu__item">
        <create-table-popover @createTable="createTable" />
      </div>

      <div class="el-tiptap-popper__menu__item__separator" />

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.addColumnBefore"
      >
        <span>向左插入一列</span>
      </div>

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.addColumnAfter"
      >
        <span>向右插入一列</span>
      </div>

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.deleteColumn"
      >
        <span>删除列</span>
      </div>

      <div class="el-tiptap-popper__menu__item__separator" />

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.addRowBefore"
      >
        <span>向上插入一行</span>
      </div>

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.addRowAfter"
      >
        <span>向下插入一行</span>
      </div>

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.deleteRow"
      >
        <span>删除行</span>
      </div>

      <div class="el-tiptap-popper__menu__item__separator" />

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !enableMergeCells }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.mergeCells"
      >
        <span>合并单元格</span>
      </div>

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !enableSplitCell }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.splitCell"
      >
        <span>拆分单元格</span>
      </div>

      <div class="el-tiptap-popper__menu__item__separator" />

      <div
        :class="{ 'el-tiptap-popper__menu__item--disabled': !isTableActive }"
        class="el-tiptap-popper__menu__item"
        @mousedown="hidePopover"
        @click="editor.commands.deleteTable"
      >
        <span>删除表格</span>
      </div>
    </div>

    <template #reference>
      <span>
        <command-button
          :is-active="isTableActive"
          :enable-tooltip="enableTooltip"
          tooltip="表格"
          :readonly="isCodeViewMode"
          icon="table"
        />
      </span>
    </template>
  </el-popover>
</template>

<script lang="ts">
import { defineComponent, ref, unref } from 'vue'
import { Editor } from '@tiptap/vue-3'
import { ElMessage, ElPopover } from 'element-plus'
import { isTableActive, enableMergeCells, enableSplitCell } from '@/utils/table'
import CommandButton from '../CommandButton.vue'
import CreateTablePopover from './CreateTablePopover.vue'
import { checkEditorFocus } from '@/utils/editor'

export default defineComponent({
  name: 'TablePopover',

  components: {
    ElPopover,
    CommandButton,
    CreateTablePopover
  },

  props: {
    editor: {
      type: Editor,
      required: true
    }
  },

  setup() {
    const enableTooltip = ref(true)
    const isCodeViewMode = ref(false)
    const popoverVisible = ref(false)
    const popoverRef = ref()

    const hidePopover = () => {
      popoverVisible.value = false
    }

    return { enableTooltip, isCodeViewMode, popoverRef, hidePopover, popoverVisible }
  },

  computed: {
    isTableActive() {
      return isTableActive(this.editor.state)
    },

    enableMergeCells() {
      return enableMergeCells(this.editor.state)
    },

    enableSplitCell() {
      return enableSplitCell(this.editor.state)
    }
  },

  methods: {
    createTable({ row, col }: { row: number; col: number }): void {
      if (!checkEditorFocus(this.editor, 'table')) {
        ElMessage.warning('请选择插入位置')
        return
      }
      this.editor.commands.insertTable({
        rows: row,
        cols: col,
        withHeaderRow: true
      })

      const totalCells = row * col
      for (let i = 0; i < totalCells; i++) {
        // 当前单元格插入空格
        this.editor.commands.insertContent(' ')
        // 如果不是最后一个单元格，则移动到下一个单元格
        if (i < totalCells - 1) {
          this.editor.commands.goToNextCell()
        }
      }

      this.hidePopover()
    }
  }
})
</script>
