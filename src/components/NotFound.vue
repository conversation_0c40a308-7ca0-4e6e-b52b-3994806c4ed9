<!-- src/views/NotFoundPage.vue -->
<template>
  <div class="not-found">
    <img src="//static-1256600262.file.myqcloud.com/xiaoin-h5/image/page-404-1.png" alt="" />
    <h1>您访问的页面走丢了</h1>
    <p>即将离开本页，{{ countdown }}秒后自动返回首页</p>
    <el-button @click="toHome" type="primary">返回首页</el-button>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { isLocalHost } from '@/utils/cross'

const countdown = ref(3) // 倒计时初始值为 3 秒
// https://xiaoin-test.xingxiai.cn/ai-editor/#/?id=1924778583864250370
const url = isLocalHost()
  ? `https://xiaoin-test.xingxiai.cn`
  : `${location.protocol}//${location.hostname}/home/<USER>
const toHome = () => {
  window.location.href = url // 跳转到外部首页
}
const startCountdown = () => {
  const interval = setInterval(() => {
    countdown.value -= 1
    if (countdown.value === 0) {
      clearInterval(interval)
      toHome()
    }
  }, 1000)
}
onMounted(() => {
  startCountdown()
})
</script>

<style lang="scss" scoped>
.not-found {
  text-align: center;
  padding: 50px;
  img {
    width: 792px;
    height: 526px;
  }
}
.not-found h1 {
  color: #333;
}
.not-found p {
  margin: 30px 0;
  font-size: 14px;
}
</style>
