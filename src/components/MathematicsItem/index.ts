import { mergeAttributes} from '@tiptap/core'
import {Extension, VueNodeViewRenderer} from '@tiptap/vue-3'
import LinkEditPanel from "../component/link-text/link-edit-panel.vue";
 

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        myCommand: {
            setMathematicsText: (text: string) => ReturnType;
      };
    }
  }
export default Extension.create({
    name: 'LinkItem',
    group: 'inline',
    inline: true,
    draggable: true,
    // 定义 editable 属性为 true
    editable: true,
 
    addAttributes() {
        return {
            text: {
                default: null,
            },
            link: {
                default: null,
            },
            settingVisible :{
                default : false,    // 默认不展示设置
            }
        }
    },
 
    parseHTML() {
        return [
            {
                tag: 'LinkDiv',
            },
        ]
    },
 
    // renderHTML({HTMLAttributes}) {
    //     return ['LinkDiv', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
    // },
     addCommands() {
       return {
        setMathematicsText: (text) => ({ chain }) => chain().setMark('Mathematics', {text}).run(),
       }
     },
    addNodeView() {
        return VueNodeViewRenderer(LinkEditPanel)
    },
})