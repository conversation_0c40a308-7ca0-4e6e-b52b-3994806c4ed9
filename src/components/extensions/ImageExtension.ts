import Image from '@tiptap/extension-image'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import ImageComponent from './ImageComponent.vue'

export interface ImageOptions {
  HTMLAttributes: Record<string, any>
  svgEdit?: boolean
}

// 扩展默认的 Image 扩展
export const CustomImage = Image.extend<ImageOptions>({
  addOptions() {
    return {
      ...this.parent?.(),
      svgEdit: false
    }
  },

  renderHTML({ HTMLAttributes }) {
    const { src } = HTMLAttributes
    // 只有启用了 svgEdit 且是 SVG 图片时才使用自定义渲染
    if (this.options.svgEdit && src?.toLowerCase().endsWith('.svg')) {
      return ['div', { class: 'svg-image-wrapper' }, ['img', HTMLAttributes]]
    }
    // 其他情况使用默认渲染
    return ['img', HTMLAttributes]
  },

  addNodeView() {
    return VueNodeViewRenderer(ImageComponent)
  }
})
