<template>
  <node-view-wrapper class="chart-wrapper" :class="{ 'node-selected': selected }">
    <div class="chart-container relative group">
      <div v-if="selected" class="absolute right-2 top-2 z-10">
        <el-button type="primary" size="small" @click="handleEdit">
          <!-- <template #icon><edit-outlined /></template> -->
          编辑
        </el-button>
      </div>
      <Bar v-if="node.attrs.type === 'bar'" :data="chartData" :options="chartOptions" />
      <Line v-else-if="node.attrs.type === 'line'" :data="chartData" :options="chartOptions" />
      <Pie v-else-if="node.attrs.type === 'pie'" :data="chartData" :options="chartOptions" />
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { Bar, Line, Pie } from 'vue-chartjs'
import { NodeViewWrapper } from '@tiptap/vue-3'
import type { NodeViewProps } from '@tiptap/vue-3'
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  LineElement,
  PointElement,
  ArcElement
} from 'chart.js'
import { Extension } from '@tiptap/core'

// 注册 ChartJS 组件
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  LineElement,
  PointElement,
  ArcElement
)

const props = defineProps<NodeViewProps>()

const handleEdit = () => {
  const extension = props.editor.extensionManager.extensions.find((ext) => ext.name === 'chart')

  if (extension?.options?.onEditChart) {
    extension.options.onEditChart({
      type: props.node.attrs.type,
      data: props.node.attrs.data
    })
  }
}

// 确保数据有效并正确格式化
const ensureValidData = (data: any) => {
  if (!data) return { labels: [], datasets: [] }

  // 如果数据已经是对象，直接返回
  if (typeof data === 'object') return data

  // 如果数据是字符串（可能是从 data-chart 属性解析的），尝试解析
  if (typeof data === 'string') {
    try {
      return JSON.parse(data)
    } catch (e) {
      console.error('Failed to parse chart data:', e)
      return { labels: [], datasets: [] }
    }
  }

  return { labels: [], datasets: [] }
}

const chartData = computed(() => ensureValidData(props.node.attrs.data))

// 监听 node.attrs 变化，确保图表数据更新
watch(
  () => props.node.attrs,
  (newAttrs) => {
    console.log('Chart attrs changed:', newAttrs)
  },
  { deep: true }
)

onMounted(() => {
  // 确保初始化时数据正确
  if (!props.node.attrs.data) {
    props.updateAttributes({
      data: { labels: [], datasets: [] }
    })
  }
})
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false
}
</script>

<style scoped>
.chart-wrapper {
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 0.5rem;
}

.node-selected {
  border-color: #4299e1;
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style>
