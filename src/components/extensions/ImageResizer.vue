<template>
  <node-view-wrapper class="image-wrapper" :class="{ 'ProseMirror-selectednode': selected }">
    <node-view-content>
      <!-- <div class="image-resizer" @mousedown="startResizing"> -->
      <img
        :src="node.attrs.src"
        :alt="node.attrs.alt"
        :title="node.attrs.title"
        :style="imageStyle"
        @dragstart.prevent
      />
      <!-- <template v-if="selected">
          <div class="resize-handle top-left" data-handle="top-left"></div>
          <div class="resize-handle top-right" data-handle="top-right"></div>
          <div class="resize-handle bottom-left" data-handle="bottom-left"></div>
          <div class="resize-handle bottom-right" data-handle="bottom-right"></div>
        </template> -->
      <!-- </div> -->
    </node-view-content>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NodeViewWrapper, NodeViewContent } from '@tiptap/vue-3'
import type { NodeViewProps } from '@tiptap/vue-3'

const props = defineProps<NodeViewProps>()

const imageStyle = computed(() => {
  const { width, height } = props.node.attrs
  return {
    width: width ? `${width}px` : 'auto',
    height: height ? `${height}px` : 'auto'
  }
})

const startResizing = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.classList.contains('resize-handle')) return

  const handle = target.dataset.handle
  const image = target.parentElement?.querySelector('img')
  if (!image) return

  const startX = event.pageX
  const startY = event.pageY
  const startWidth = image.offsetWidth
  const startHeight = image.offsetHeight

  const onMouseMove = (e: MouseEvent) => {
    const deltaX = e.pageX - startX
    const deltaY = e.pageY - startY

    let newWidth = startWidth
    let newHeight = startHeight

    switch (handle) {
      case 'top-left':
        newWidth = startWidth - deltaX
        newHeight = startHeight - deltaY
        break
      case 'top-right':
        newWidth = startWidth + deltaX
        newHeight = startHeight - deltaY
        break
      case 'bottom-left':
        newWidth = startWidth - deltaX
        newHeight = startHeight + deltaY
        break
      case 'bottom-right':
        newWidth = startWidth + deltaX
        newHeight = startHeight + deltaY
        break
    }

    // 保持宽高比
    const aspectRatio = startWidth / startHeight
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      newHeight = newWidth / aspectRatio
    } else {
      newWidth = newHeight * aspectRatio
    }

    // 更新图片尺寸
    props.updateAttributes({
      width: Math.round(newWidth),
      height: Math.round(newHeight)
    })
  }

  const onMouseUp = () => {
    // document.removeEventListener('mousemove', onMouseMove)
    // document.removeEventListener('mouseup', onMouseUp)
  }

  //   document.addEventListener('mousemove', onMouseMove)
  //   document.addEventListener('mouseup', onMouseUp)
}
</script>

<style scoped>
.image-wrapper {
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 0.5rem;
}

.image-wrapper.ProseMirror-selectednode {
  border-color: #4299e1;
}

.image-resizer {
  display: inline-flex;
  position: relative;
  flex-grow: 0;
}

.image-resizer img {
  max-width: 100%;
  height: auto;
  display: block;
}

.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 1px solid #4299e1;
  background: white;
  border-radius: 2px;
  z-index: 10;
}

.resize-handle.top-left {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle.top-right {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle.bottom-left {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-handle.bottom-right {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}
</style>
