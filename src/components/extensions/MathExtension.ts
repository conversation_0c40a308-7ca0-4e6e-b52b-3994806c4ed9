import { Node, mergeAttributes } from '@tiptap/core'
import katex from 'katex'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import 'katex/dist/katex.min.css'
import MathComponent from './MathComponent.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    mathInline: {
      setMathInline: (formula: string) => ReturnType
    }
    mathBlock: {
      setMathBlock: (formula: string) => ReturnType
    }
  }
}

export interface MathOptions {
  HTMLAttributes: Record<string, any>
  onEdit?: (props: { formula: string; type: 'inline' | 'block' }) => void
}

// 行内公式扩展
export const MathInline = Node.create<MathOptions>({
  name: 'mathInline',
  group: 'inline',
  inline: true,
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
      onEdit: undefined
    }
  },

  addAttributes() {
    return {
      formula: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-formula'),
        renderHTML: (attributes) => {
          return {
            'data-formula': attributes.formula
          }
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-type="math-inline"]'
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const formula = HTMLAttributes.formula || ''
    try {
      const renderedFormula = katex.renderToString(formula, { throwOnError: false })
      return [
        'span',
        mergeAttributes({ 'data-type': 'math-inline', 'data-formula': formula }, HTMLAttributes, {
          class: 'math-inline'
        }),
        renderedFormula
      ]
    } catch (error) {
      console.error('KaTeX rendering error:', error)
      return [
        'span',
        mergeAttributes({ 'data-type': 'math-inline', 'data-formula': formula }, HTMLAttributes),
        formula
      ]
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(MathComponent)
  },

  addCommands() {
    return {
      setMathInline:
        (formula: string) =>
        ({ commands, chain }) => {
          return chain()
            .insertContent({
              type: this.name,
              attrs: { formula }
            })
            .run()
        }
    }
  }
})

// 块级公式扩展
export const MathBlock = Node.create<MathOptions>({
  name: 'mathBlock',
  group: 'block',
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
      onEdit: undefined
    }
  },

  addAttributes() {
    return {
      formula: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-formula'),
        renderHTML: (attributes) => {
          return {
            'data-formula': attributes.formula
          }
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="math-block"]'
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const formula = HTMLAttributes.formula || ''
    try {
      const renderedFormula = katex.renderToString(formula, {
        displayMode: true,
        throwOnError: false
      })
      return [
        'div',
        mergeAttributes({ 'data-type': 'math-block', 'data-formula': formula }, HTMLAttributes, {
          class: 'math-block'
        }),
        renderedFormula
      ]
    } catch (error) {
      console.error('KaTeX rendering error:', error)
      return [
        'div',
        mergeAttributes({ 'data-type': 'math-block', 'data-formula': formula }, HTMLAttributes),
        formula
      ]
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(MathComponent)
  },

  addCommands() {
    return {
      setMathBlock:
        (formula: string) =>
        ({ commands, chain }) => {
          return chain()
            .insertContent({
              type: this.name,
              attrs: { formula }
            })
            .run()
        }
    }
  }
})
