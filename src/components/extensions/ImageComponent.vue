<template>
  <node-view-wrapper class="image-wrapper" :class="{ 'node-selected': selected }">
    <div class="image-container relative group">
      <template v-if="isSvgImage">
        <div v-if="selected" class="absolute right-2 top-2 z-10">
          <el-button type="primary" size="small" @click="handleEdit" :loading="isLoading">
            编辑
          </el-button>
        </div>
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
        </div>
      </template>
      <img
        :src="node.attrs.src"
        :alt="node.attrs.alt"
        :title="node.attrs.title"
        :style="imageStyle"
        class="max-w-full"
        @dragstart.prevent
      />
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper } from '@tiptap/vue-3'
import type { NodeViewProps } from '@tiptap/vue-3'
import { ref, computed } from 'vue'
import { useSchematicSvgStore } from '@/stores/schematicSvgStore'

interface SvgData {
  schematicData: {
    title: string
    items: Array<{ name: string; desc: string; icon: string | null }>
    fileName: string
    count: number
    type: string
    nums: string
  }
  imageUrl: string
  svgWidth: number
  svgHeight: number
  docId: string
}

const props = defineProps<NodeViewProps>()
const schematicSvgStore = useSchematicSvgStore()
const svgData = ref<SvgData | null>(null)
const isLoading = ref(false)

// 判断是否有SVG数据且启用了SVG编辑功能
const isSvgImage = computed(() => {
  return props.node.attrs.svgData && props.extension.options.svgEdit
})

// 计算图片样式
const imageStyle = computed(() => {
  const { width, height } = props.node.attrs
  return {
    width: width ? `${width}px` : 'auto',
    height: height ? `${height}px` : 'auto'
  }
})

// 调试：检查节点属性中的SVG数据
const debugNodeAttrs = () => {
  console.log('节点所有属性:', props.node.attrs)
  console.log('SVG数据:', props.node.attrs.svgData)
  console.log('图片URL:', props.node.attrs.src)
}

// 在组件挂载时调试
if (isSvgImage.value) {
  debugNodeAttrs()
}

// 处理编辑按钮点击
const handleEdit = async () => {
  if (!isSvgImage.value || isLoading.value) return

  isLoading.value = true
  try {
    // 直接从节点属性中获取 SVG 数据（因为 isSvgImage 已经确保了 svgData 存在）
    const data = props.node.attrs.svgData as SvgData
    console.log('从节点属性获取SVG数据:', data)

    if (data && data.schematicData) {
      svgData.value = data
      schematicSvgStore.setIsEdit(true)
      schematicSvgStore.setSchematicData(data.schematicData)
      schematicSvgStore.openModal()
      console.log('成功打开SVG编辑器，数据:', data.schematicData)
    } else {
      console.warn('SVG数据格式不正确:', data)
    }
  } catch (error) {
    console.error('获取SVG数据失败:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.image-wrapper {
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 0.5rem;
}

.node-selected {
  border-color: #4299e1;
}

.image-container {
  width: 100%;
  display: inline-block;
  position: relative;
}

.image-container img {
  max-width: 100%;
  height: auto;
  display: block;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #4299e1;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
