@import './variables.scss';

.el-tiptap-editor {
    $editor: &;

    &__command-button {
        border: 1px solid transparent;
        box-sizing: border-box;
        align-items: center;
        border-radius: 50%;
        color: $primary-text-color;
        cursor: pointer;
        display: flex;
        justify-content: center;
        height: 40px;
        margin: 2px;
        outline: 0;
        transition: all 0.2s ease-in-out;
        width: 40px;

        &:hover {
            background-color: darken($light-background-color, 5%);
        }

        &--active {
            background-color: $extra-light-primary-color;
            color: $primary-color;
        }

        &--readonly {
            cursor: default;
            opacity: 0.3;
            pointer-events: none;

            &:hover {
                background-color: transparent;
            }
        }
    }
}

.el-tiptap-dropdown-popper {
    .el-dropdown-menu__item {
        padding: 0;
    }
}

.el-tiptap-dropdown-menu {
    $menu: &;

    #{$menu}__item {
        color: $primary-text-color;
        line-height: 1.5;
        padding: 5px 20px;
        width: 100%;

        [data-item-type='heading'] {
            margin-bottom: 0;
            margin-top: 0;
        }

        &--active {
            background-color: $extra-light-primary-color;
            color: $primary-color;
        }
    }
}

.el-tiptap-popper {
    width: auto !important;

    &.el-popper {
        min-width: 0;
    }

    &__menu {
        &__item {
            color: $primary-text-color;
            cursor: pointer;
            padding: 8px 0;

            &:hover {
                color: $primary-color;
            }

            &--active {
                color: $primary-color;
            }

            &--disabled {
                cursor: default;
                opacity: 0.2;

                &:hover {
                    color: inherit;
                }
            }

            &__separator {
                border-top: 1px solid $border-color;
                height: 0;
                margin: 5px 0;
                width: 100%;
            }
        }
    }
}

.el-tiptap-upload {
    $root: &;

    display: flex;

    .el-upload {
        flex-grow: 1;
    }

    .el-upload-dragger {
        align-items: center;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        justify-content: center;
        height: 300px;
        width: 100%;

        #{$root}__icon {
            font-size: 50px;
            margin-bottom: 10px;
        }

        &:hover {
            #{$root}__icon {
                color: $primary-color;
            }
        }
    }
}

// color
.color-set {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 240px;

    .color {
        border-radius: 50%;
        box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 3px -2px,
            rgba(0, 0, 0, 0.14) 0px 3px 4px 0px, rgba(0, 0, 0, 0.12) 0px 1px 8px 0px;
        box-sizing: border-box;
        color: $white-color;
        height: 30px;
        transition: all 0.2s ease-in-out;
        width: 30px;

        &__wrapper {
            align-items: center;
            box-sizing: border-box;
            cursor: pointer;
            display: flex;
            flex: 0 0 12.5%;
            justify-content: center;
            padding: 5px;
        }

        &:hover,
        &--selected {
            border: 2px solid $white-color;
            transform: scale(1.3);
        }

        &--remove {
            position: relative;

            &:hover {
                &::before {
                    transform: rotate(-45deg);
                }

                &::after {
                    transform: rotate(45deg);
                }
            }

            &::before,
            &::after {
                background-color: $danger-color;
                bottom: 0;
                content: '';
                left: 50%;
                position: absolute;
                margin: 2px 0;
                top: 0;
                transform: translateX(-50%);
                transition: all 0.2s ease-in-out;
                width: 2px;
            }
        }
    }
}

.color-hex {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10px;

    #{&}__button {
        margin-left: 10px;
        padding-left: 15px;
        padding-right: 15px;
    }
}

.table-grid-size-editor {
    $root: &;

    &__body {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    &__row {
        display: flex;
    }

    &__cell {
        background-color: $white-color;
        padding: 5px;

        &__inner {
            border: 1px solid $border-color;
            box-sizing: border-box;
            border-radius: 2px;
            height: 16px;
            padding: 4px;
            width: 16px;
        }

        &--selected {
            #{$root}__cell__inner {
                background-color: $extra-light-primary-color;
                border-color: $primary-color;
            }
        }
    }

    &__footer {
        margin-top: 5px;
        text-align: center;
    }
}

.el-tiptap-edit-image-dialog {
    .el-form-item:last-child {
        margin-bottom: 0;
    }

    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none; // Correct the cursor style of increment and decrement buttons in Chrome
    }
}

.el-tiptap-edit-link-dialog {
    .el-form-item:last-child {
        margin-bottom: 0;
    }
}

.el-popper.el-tiptap-image-popper {
    background-color: $white-color;
    border-radius: 8px;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
        0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
    min-width: 0;
    padding: 5px;

    .image-bubble-menu {
        align-items: center;
        display: flex;
        flex-direction: row;
    }
}