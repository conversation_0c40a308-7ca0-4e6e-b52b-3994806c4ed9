.ai-editor-theme {
    .logo {
        margin-right: 40px;
        width: 170px;
        height: 42px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;
    }
}

.xiaoin-theme {
    .logo {
        background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-h5/bg/web-logo.png);
    }
}

.ybxz-theme {
    .logo {
        background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-h5/bg/web-logo-ybxz.png);
    }
}

.payment-dialog-wrap,
.el-dialog {
    background: white !important;
}

.payment-dialog-test {
    display: none !important;
    opacity: 0;
    position: fixed;
    top: -10000px;
}