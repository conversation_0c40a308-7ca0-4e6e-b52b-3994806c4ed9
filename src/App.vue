<script setup lang="ts">
import { RouterView } from 'vue-router'
import LoginModal from '@/components/LoginModal.vue'
import { isXiaoin } from '@/utils/utils'
</script>

<template>
  <!-- <header>
    <img alt="Vue logo" class="logo" src="@/assets/logo.svg" width="125" height="125" />

    <div class="wrapper">
      <HelloWorld msg="You did it! " />

      <nav>
        <RouterLink to="/">Home</RouterLink>
        <RouterLink to="/about">About</RouterLink>
      </nav>
    </div>
  </header> -->
  <div :class="isXiaoin() ? 'ai-editor-theme xiaoin-theme' : 'ai-editor-theme ybxz-theme'">
    <RouterView />
    <LoginModal />
  </div>
</template>

<style scoped>
body {
  margin: 0;
  padding: 0;
  /* background-color: aqua; */
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* text-align: center; */
  color: #2c3e50;
  margin: 0;
  padding: 0;
  background-color: aqua;
}
</style>
