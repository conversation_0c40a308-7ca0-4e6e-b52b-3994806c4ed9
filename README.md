# xiaoin-editor

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

```sh
npm run build:test
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

# https://app.quicktype.io/#/ quicktype

# http://element-plus.org/zh-CN/component/overview.html element-plus地址

# https://www.nowcoder.com/discuss/514643955691151360

https://github.com/HMarzban/extension-hyperlink#test-drive-with-our-demo-

# 一笔写作

```sh
npm run dev:ybxz
npm run build:test:ybxz
npm run build:ybxz
```

# 生产发布到 xiaoin-1256600262/ai-editor/， 访问地址 https://xiaoin.com.cn/ai-editor/#/
