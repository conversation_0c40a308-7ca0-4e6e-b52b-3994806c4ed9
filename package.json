{"name": "<PERSON><PERSON><PERSON><PERSON>-editor", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "dev:ybxz": "vite --mode development_ybxz", "build": "run-p \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build:test": "run-p \"build-only-test {@}\" --", "build-only-test": "vite build --mode development", "build:test:ybxz": "run-p type-check \"build-only-test-ybxz {@}\" --", "build-only-test-ybxz": "vite build --mode development_ybxz", "build:ybxz": "run-p type-check \"build-only-ybxz {@}\" --", "build-only-ybxz": "vite build --mode production_ybxz", "build-only": "vite build --mode production", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "upload:test": "ypg-qcloud-cos-uploader --useTest"}, "dependencies": {"@editorjs/editorjs": "^2.29.1", "@editorjs/header": "^2.8.1", "@editorjs/image": "^2.9.0", "@editorjs/list": "^1.9.0", "@editorjs/quote": "^2.6.0", "@editorjs/table": "^2.3.0", "@element-plus/icons-vue": "^2.3.1", "@svgdotjs/svg.js": "^3.2.4", "@svgdotjs/svg.topath.js": "^2.0.3", "@tiptap-pro/extension-mathematics": "^2.8.7", "@tiptap-pro/extension-table-of-contents": "^2.8.7", "@tiptap-pro/extension-unique-id": "^2.8.7", "@tiptap/core": "^2.12.0", "@tiptap/extension-dropcursor": "^2.4.0", "@tiptap/extension-image": "^2.4.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-superscript": "^2.4.0", "@tiptap/extension-table": "^2.4.0", "@tiptap/extension-table-cell": "^2.4.0", "@tiptap/extension-table-header": "^2.4.0", "@tiptap/extension-table-row": "^2.4.0", "@tiptap/extension-text-align": "^2.4.0", "@tiptap/extension-underline": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@tiptap/vue-3": "^2.4.0", "@types/katex": "^0.16.7", "@vue/compiler-sfc": "^3.4.27", "@vueuse/core": "^10.9.0", "ali-oss": "^6.23.0", "ant-design-vue": "^4.1.2", "axios": "^1.7.2", "chart.js": "^4.4.9", "cos-js-sdk-v5": "^1.8.0", "crypto-js": "^4.2.0", "element-plus": "^2.7.3", "idb": "^8.0.0", "katex": "^0.16.22", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "pinia": "^2.1.7", "qs": "^6.12.1", "tiptap-extension-resize-image": "^1.1.4", "vue": "^3.4.21", "vue-chartjs": "^5.3.2", "vue-router": "^4.3.0", "ygp-qcloud-cos-uploader": "^1.1.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/crypto-js": "^4.2.2", "@types/jsdom": "^21.1.6", "@types/lodash": "^4.17.17", "@types/markdown-it": "^14.1.1", "@types/node": "^20.12.5", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.0.0", "npm-run-all2": "^6.1.2", "prettier": "^3.2.5", "sass": "^1.77.2", "typescript": "~5.4.0", "vite": "^5.2.8", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-devtools": "^7.7.6", "vitest": "^1.4.0", "vue-tsc": "^2.0.11"}, "cosUploader": {"secretId": "AKIDYu4nLlMBAsKwIOtk3C4yrWu1Fyd3qxmR", "secretKey": "f7JHyeYDbnkRu662i7E4eXAbzB6j5LLC", "bucket": "xiaoin-dev-1256600262", "region": "ap-shanghai", "path": "dist", "prefix": "ai-editor", "baseUrl": "https://xiaoin-dev.inschool.top"}, "cosUploaderTest": {"secretId": "AKIDYu4nLlMBAsKwIOtk3C4yrWu1Fyd3qxmR", "secretKey": "f7JHyeYDbnkRu662i7E4eXAbzB6j5LLC", "bucket": "xiaoin-dev-1256600262", "region": "ap-shanghai", "path": "dist", "prefix": "ai-editor", "baseUrl": "https://xiaoin-dev.inschool.top"}}