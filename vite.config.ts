import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import VueDevTools from 'vite-plugin-vue-devtools'
import { createHtmlPlugin } from 'vite-plugin-html'

export default defineConfig(({ mode }) => {
  const root = process.cwd()
  const env = loadEnv(mode, root)

  return {
    base: '/ai-editor/',
    plugins: [
      vue(),
      VueDevTools(),
      createHtmlPlugin({
        inject: {
          data: {
            icon: env.VITE_APP_API_ICON,
            title: env.VITE_APP_API_TITLE,
            keywords: env.VITE_APP_API_KEYWORDS,
            description: env.VITE_APP_API_DESCRIPTION
          }
        }
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0', // 服务器监听的主机地址
      port: 5173, // 服务器监听的端口
      hmr: true
    }
  }
})

// export default defineConfig({
//   base: '/ai-editor/',
//   plugins: [
//     vue(),
//     VueDevTools(),
//     //修改index.html标题
//     createHtmlPlugin({
//         inject: {
//             data: {
//                 title: env.VITE_APP_API_TITLE,
//             },
//         },
//     }),
//   ],
//   resolve: {
//     alias: {
//       '@': fileURLToPath(new URL('./src', import.meta.url))
//     }
//   },
//   server: {
//     host: '0.0.0.0', // 服务器监听的主机地址
//     port: 5173, // 服务器监听的端口
//     hmr: true,
//   }
// })
